<?php
/**
 * Bamboo Web Application - Withdraw Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        $transaction_id = (int)($_POST['transaction_id'] ?? 0);

        if ($action === 'approve' && $transaction_id > 0) {
            $transaction = fetchRow('SELECT * FROM transactions WHERE id = ?', [$transaction_id]);
            if ($transaction && $transaction['status'] === 'pending') {
                try {
                    $db = getDB();
                    $stmt = $db->prepare("CALL ProcessTransaction(?, 'withdrawal', ?, 'Admin approved withdrawal')");
                    $stmt->execute([$transaction['user_id'], $transaction['amount']]);
                    updateRecord('transactions', ['status' => 'completed'], 'id = ?', [$transaction_id]);
                    showSuccess('Withdrawal approved successfully!');
                } catch (PDOException $e) {
                    showError('Failed to process transaction: ' . $e->getMessage());
                }
            }
        } elseif ($action === 'reject' && $transaction_id > 0) {
            $transaction = fetchRow('SELECT * FROM transactions WHERE id = ?', [$transaction_id]);
            if ($transaction && $transaction['status'] === 'pending') {
                try {
                    $db = getDB();
                    $stmt = $db->prepare("CALL ProcessTransaction(?, 'deposit', ?, 'Admin rejected withdrawal. Funds returned.')");
                    $stmt->execute([$transaction['user_id'], $transaction['amount']]);
                    updateRecord('transactions', ['status' => 'failed'], 'id = ?', [$transaction_id]);
                    showSuccess('Withdrawal rejected successfully!');
                } catch (PDOException $e) {
                    showError('Failed to process transaction: ' . $e->getMessage());
                }
            }
        }
        redirect('admin/withdraw/');
        exit();
    }
}

$page_title = 'Withdraw Management';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css',
    BASE_URL . 'admin/withdraw/withdraw.css'
];
include '../includes/admin_header.php';

// Pagination with defensive programming
$page_param = $_GET['page'] ?? 1;

// Robust page validation
if (!is_numeric($page_param) || $page_param < 1) {
    $page = 1;
} else {
    $page = (int)$page_param;
}

$limit = 20;
$page = max(1, (int)$page); // Ensure page is at least 1
$offset = ($page - 1) * $limit;

// Search and Filter
$search_term = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');

$where_clause = 't.type = \'withdrawal\'';
$params = [];

if (!empty($search_term)) {
    $where_clause .= ' AND (u.username LIKE ? OR t.transaction_id LIKE ?)';
    $params = array_merge($params, ["%$search_term%", "%$search_term%"]);
}

if (!empty($status_filter)) {
    $where_clause .= ' AND t.status = ?';
    $params[] = $status_filter;
}

$total_transactions = getRecordCount('transactions t LEFT JOIN users u ON t.user_id = u.id', $where_clause, $params);
$total_transactions = (int)$total_transactions; // Ensure integer
$total_pages = (int)ceil($total_transactions / $limit);

// Validate page doesn't exceed total pages
if ($page > $total_pages && $total_pages > 0) {
    $page = $total_pages;
    $offset = ($page - 1) * $limit;
}

$query = "SELECT t.*, u.username 
          FROM transactions t 
          LEFT JOIN users u ON t.user_id = u.id
          WHERE $where_clause
          ORDER BY t.created_at DESC
          LIMIT $limit OFFSET $offset";

$transactions = fetchAll($query, $params);
$statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled'];

// Final type casting before template to prevent string-int operation errors
$page = (int)$page;
$limit = (int)$limit;

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="withdraw-header">
                    <h1>Withdraw Management</h1>
                    <p class="text-muted">Review and manage all withdrawal requests</p>
                </div>

                <div class="withdraw-card card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-bank me-2"></i>All Withdrawals (<?php echo $total_transactions; ?>)
                            </h5>
                            <form action="" method="GET" class="filter-form">
                                <input type="text" name="search" class="form-control" placeholder="Search by user or TXN ID..." value="<?php echo htmlspecialchars($search_term); ?>">
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    <?php foreach ($statuses as $status): ?>
                                        <option value="<?php echo $status; ?>" <?php echo ($status_filter == $status) ? 'selected' : ''; ?>><?php echo ucfirst($status); ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($transactions)): ?>
                            <div class="empty-state">
                                <i class="bi bi-inbox"></i>
                                <h5>No Withdrawals Found</h5>
                                <p>There are no withdrawal requests matching your criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="withdraw-table table">
                                <thead>
                                    <tr>
                                        <th>S/N</th>
                                        <th>User</th>
                                        <th>Transaction ID</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($transactions)): ?>
                                        <?php
                                        // Ensure proper numbering calculation
                                        $current_page = max(1, (int)$page);
                                        $items_per_page = max(1, (int)$limit);
                                        $sn = ($current_page - 1) * $items_per_page + 1;
                                        ?>
                                        <?php foreach ($transactions as $txn): ?>
                                            <tr>
                                                <td>
                                                    <div class="serial-number"><?php echo $sn++; ?></div>
                                                </td>
                                                <td>
                                                    <div class="user-info">
                                                        <div class="username"><?php echo ucfirst(htmlspecialchars($txn['username'] ?? 'N/A')); ?></div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="transaction-id"><?php echo htmlspecialchars($txn['transaction_id'] ?? 'N/A'); ?></div>
                                                </td>
                                                <td>
                                                    <div class="withdraw-amount text-success fw-bold"><?php echo formatCurrency($txn['amount']); ?></div>
                                                </td>
                                                <td>
                                                    <span class="status-badge badge bg-<?php echo getStatusBadgeClass($txn['status']); ?>">
                                                        <?php echo ucfirst($txn['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="transaction-date"><?php echo formatDate($txn['created_at']); ?></div>
                                                </td>
                                                <td>
                                                    <?php if ($txn['status'] === 'pending'): ?>
                                                        <div class="action-buttons">
                                                            <form action="" method="POST" class="d-inline">
                                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                                <input type="hidden" name="transaction_id" value="<?php echo $txn['id']; ?>">
                                                                <button type="submit" name="action" value="approve" class="btn btn-success">
                                                                    <i class="bi bi-check-circle me-1"></i>Approve
                                                                </button>
                                                            </form>
                                                            <form action="" method="POST" class="d-inline">
                                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                                <input type="hidden" name="transaction_id" value="<?php echo $txn['id']; ?>">
                                                                <button type="submit" name="action" value="reject" class="btn btn-danger">
                                                                    <i class="bi bi-x-circle me-1"></i>Reject
                                                                </button>
                                                            </form>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center text-muted py-4">
                                                <i class="bi bi-inbox me-2"></i>No withdrawal requests found.
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-enhanced">
                            <?php renderPagination($page, $total_pages, 'search=' . urlencode($search_term) . '&status=' . urlencode($status_filter)); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>