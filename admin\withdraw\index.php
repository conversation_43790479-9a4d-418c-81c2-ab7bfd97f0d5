<?php
/**
 * Bamboo Web Application - Withdraw Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        $transaction_id = (int)($_POST['transaction_id'] ?? 0);

        if ($action === 'approve' && $transaction_id > 0) {
            $transaction = fetchRow('SELECT * FROM transactions WHERE id = ?', [$transaction_id]);
            if ($transaction && $transaction['status'] === 'pending') {
                try {
                    $db = getDB();
                    $stmt = $db->prepare("CALL ProcessTransaction(?, 'withdrawal', ?, 'Admin approved withdrawal')");
                    $stmt->execute([$transaction['user_id'], $transaction['amount']]);
                    updateRecord('transactions', ['status' => 'completed'], 'id = ?', [$transaction_id]);
                    showSuccess('Withdrawal approved successfully!');
                } catch (PDOException $e) {
                    showError('Failed to process transaction: ' . $e->getMessage());
                }
            }
        } elseif ($action === 'reject' && $transaction_id > 0) {
            $transaction = fetchRow('SELECT * FROM transactions WHERE id = ?', [$transaction_id]);
            if ($transaction && $transaction['status'] === 'pending') {
                try {
                    $db = getDB();
                    $stmt = $db->prepare("CALL ProcessTransaction(?, 'deposit', ?, 'Admin rejected withdrawal. Funds returned.')");
                    $stmt->execute([$transaction['user_id'], $transaction['amount']]);
                    updateRecord('transactions', ['status' => 'failed'], 'id = ?', [$transaction_id]);
                    showSuccess('Withdrawal rejected successfully!');
                } catch (PDOException $e) {
                    showError('Failed to process transaction: ' . $e->getMessage());
                }
            }
        }
        redirect('admin/withdraw/');
        exit();
    }
}

$page_title = 'Withdraw Management';
include '../includes/admin_header.php';

// Pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Search and Filter
$search_term = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');

$where_clause = 't.type = \'withdrawal\'';
$params = [];

if (!empty($search_term)) {
    $where_clause .= ' AND (u.username LIKE ? OR t.transaction_id LIKE ?)';
    $params = array_merge($params, ["%$search_term%", "%$search_term%"]);
}

if (!empty($status_filter)) {
    $where_clause .= ' AND t.status = ?';
    $params[] = $status_filter;
}

$total_transactions = getRecordCount('transactions t LEFT JOIN users u ON t.user_id = u.id', $where_clause, $params);
$total_pages = ceil($total_transactions / $limit);

$query = "SELECT t.*, u.username 
          FROM transactions t 
          LEFT JOIN users u ON t.user_id = u.id
          WHERE $where_clause
          ORDER BY t.created_at DESC
          LIMIT $limit OFFSET $offset";

$transactions = fetchAll($query, $params);
$statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled'];

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Withdraw Management</h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">All Withdrawals (<?php echo $total_transactions; ?>)</h5>
                        <form action="" method="GET" class="d-flex mt-3">
                            <input type="text" name="search" class="form-control me-2" placeholder="Search by user or TXN ID..." value="<?php echo htmlspecialchars($search_term); ?>">
                            <select name="status" class="form-select me-2">
                                <option value="">All Statuses</option>
                                <?php foreach ($statuses as $status): ?>
                                    <option value="<?php echo $status; ?>" <?php echo ($status_filter == $status) ? 'selected' : ''; ?>><?php echo ucfirst($status); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="btn btn-primary"><i class="bi bi-search"></i></button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Transaction ID</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($transactions)): foreach ($transactions as $txn): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($txn['username']); ?></td>
                                            <td><?php echo htmlspecialchars($txn['transaction_id']); ?></td>
                                            <td><?php echo formatCurrency($txn['amount']); ?></td>
                                            <td><span class="badge bg-<?php echo getStatusBadgeClass($txn['status']); ?>"><?php echo ucfirst($txn['status']); ?></span></td>
                                            <td><?php echo formatDate($txn['created_at']); ?></td>
                                            <td>
                                                <?php if ($txn['status'] === 'pending'): ?>
                                                    <form action="" method="POST" class="d-inline">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <input type="hidden" name="transaction_id" value="<?php echo $txn['id']; ?>">
                                                        <button type="submit" name="action" value="approve" class="btn btn-sm btn-success">Approve</button>
                                                    </form>
                                                    <form action="" method="POST" class="d-inline">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <input type="hidden" name="transaction_id" value="<?php echo $txn['id']; ?>">
                                                        <button type="submit" name="action" value="reject" class="btn btn-sm btn-danger">Reject</button>
                                                    </form>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; else: ?>
                                        <tr><td colspan="6" class="text-center text-muted">No transactions found.</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php renderPagination($page, $total_pages, 'search=' . urlencode($search_term) . '&status=' . urlencode($status_filter)); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>