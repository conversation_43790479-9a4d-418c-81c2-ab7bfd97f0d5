<?php
/**
 * Bamboo Web Application - Edit Member
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT u.*, i.username as inviter_username, i.phone as inviter_phone, i.id as inviter_id FROM users u LEFT JOIN users i ON u.invited_by = i.id WHERE u.id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

$action = $_GET['action'] ?? 'edit';

// Fetch all agents (superior) for dropdown
$all_agents = fetchAll("SELECT * FROM superiors ORDER BY name ASC");

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
        if (!verifyCSRFToken($_POST['csrf_token'])) {
            showError('Invalid security token. Please try again.');
        } else {
            try {
                switch ($action) {
                    case 'adjust_balance':
                        $amount = (float)($_POST['amount'] ?? 0);
                        $type = $_POST['type'] ?? '';
                        if ($amount > 0 && in_array($type, ['addition', 'deduction'])) {
                            $admin_id = $_SESSION['admin_id'] ?? null;
                            $admin_username = $_SESSION['admin_username'] ?? 'Unknown';
                            $order_no = generateOrderNo(); // Use the global function
                            $payment_channel = 'Admin Panel';
                            $state = 'completed'; // Admin adjustments are always completed

                            $success = adjustUserBalance($user_id, $amount, $type, $admin_id, $order_no, $payment_channel, $admin_username, $state);

                            if ($success) {
                                showSuccess('Balance adjusted successfully!');
                                $message = 'Balance adjusted successfully!';
                            } else {
                                showError('Failed to adjust balance.');
                                $message = 'Failed to adjust balance.';
                            }
                        } else {
                            showError('Invalid amount or type.');
                            $success = false;
                            $message = 'Invalid amount or type.';
                        }
                        if (isAjaxRequest()) {
                            jsonResponse([
                                'success' => $success,
                                'message' => $message
                            ]);
                        } else {
                            // Redirect to Basic Information tab after adjustment
                            redirect('admin/member_management/edit.php?id=' . $user_id . '&action=edit');
                        }
                        exit();
                    case 'credit_score':
                        $score = (int)($_POST['score'] ?? 0);
                        if (updateRecord('users', ['credit_score' => $score], 'id = ?', [$user_id])) {
                            showSuccess('Credit score updated successfully!');
                        } else {
                            showError('Failed to update credit score.');
                        }
                        redirect('admin/member_management/edit.php?id=' . $user_id . '&action=edit');
                        break;
                    case 'change_password':
                        $new_password = $_POST['new_password'] ?? '';
                        if (!empty($new_password)) {
                            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                            if (updateRecord('users', ['password' => $hashed_password], 'id = ?', [$user_id])) {
                                showSuccess('Login password updated successfully!');
                            } else {
                                showError('Failed to update login password.');
                            }
                        } else {
                            showError('New password cannot be empty.');
                        }
                        redirect('admin/member_management/edit.php?id=' . $user_id . '&action=edit');
                        break;
                    case 'change_payment_password':
                        $new_payment_password = $_POST['new_payment_password'] ?? '';
                        if (!empty($new_payment_password)) {
                            $hashed_payment_password = password_hash($new_payment_password, PASSWORD_DEFAULT);
                            if (updateRecord('users', ['payment_password' => $hashed_payment_password], 'id = ?', [$user_id])) {
                                showSuccess('Payment password updated successfully!');
                            } else {
                                showError('Failed to update payment password.');
                            }
                        } else {
                            showError('New payment password cannot be empty.');
                        }
                        redirect('admin/member_management/edit.php?id=' . $user_id . '&action=edit');
                        break;
                    case 'upload_avatar':
                        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                            $upload_result = handleFileUpload($_FILES['avatar'], 'uploads/avatars/', ['jpg', 'jpeg', 'png']);
                            if ($upload_result['success']) {
                                // Delete old avatar if exists
                                if (!empty($user['avatar'])) {
                                    $old_avatar_path = '../../uploads/avatars/' . $user['avatar'];
                                    if (file_exists($old_avatar_path)) {
                                        unlink($old_avatar_path);
                                    }
                                }

                                if (updateRecord('users', ['avatar' => $upload_result['filename']], 'id = ?', [$user_id])) {
                                    showSuccess('Avatar uploaded successfully!');
                                } else {
                                    showError('Failed to update avatar in database.');
                                }
                            } else {
                                showError('Avatar upload failed: ' . $upload_result['message']);
                            }
                        } else {
                            showError('No avatar file uploaded or upload error.');
                        }
                        redirect('admin/member_management/edit.php?id=' . $user_id . '&action=edit');
                        break;
                    case 'disable':
                        if (updateRecord('users', ['status' => 'suspended'], 'id = ?', [$user_id])) {
                            showSuccess('Account disabled successfully!');
                        } else {
                            showError('Failed to disable account.');
                        }
                        break;
                    case 'wallet_info':
                        $usdt_wallet_address = sanitizeInput($_POST['usdt_wallet_address'] ?? '');
                        $exchange_name = sanitizeInput($_POST['exchange_name'] ?? '');
                        $update_data = [
                            'usdt_wallet_address' => $usdt_wallet_address,
                            'exchange_name' => $exchange_name
                        ];
                        if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                            showSuccess('Wallet info updated successfully!');
                            redirect('admin/member_management/edit.php?id=' . $user_id . '&action=wallet_info');
                        } else {
                            showError('Failed to update wallet info.');
                            redirect('admin/member_management/edit.php?id=' . $user_id . '&action=wallet_info');
                        }
                        exit();
                    case 'edit': // Explicitly handle the 'edit' action
                        $username = sanitizeInput($_POST['username'] ?? '');
                        $email = sanitizeInput($_POST['email'] ?? '');
                        $phone = sanitizeInput($_POST['phone'] ?? '');
                        $vip_level = (int)($_POST['vip_level'] ?? 0);
                        $status = sanitizeInput($_POST['status'] ?? '');
                        $balance = (float)($_POST['balance'] ?? 0.0);
                        $frozen_balance = (float)($_POST['frozen_balance'] ?? 0.0);
                        $commission_balance = (float)($_POST['commission_balance'] ?? 0.0);
                        $agent_code = sanitizeInput($_POST['agent_code'] ?? '');
                        $agent_name = null;
                        $agent_phone = null;
                        if (!empty($agent_code)) {
                            $agent = fetchRow('SELECT name, phone FROM superiors WHERE invitation_code = ?', [$agent_code]);
                            if ($agent) {
                                $agent_name = $agent['name'];
                                $agent_phone = $agent['phone'];
                            }
                        }

                        $update_data = [
                            'username' => $username,
                            'email' => $email,
                            'phone' => $phone,
                            'vip_level' => $vip_level,
                            'status' => $status,
                            'balance' => $balance,
                            'frozen_balance' => $frozen_balance,
                            'commission_balance' => $commission_balance,
                            'invited_by' => $agent_code
                        ];

                        if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                            // Update invited_count for the agent
                            if (!empty($agent_code)) {
                                $db = getDB();
                                $db->prepare("UPDATE superiors SET invited_count = (SELECT COUNT(*) FROM users WHERE invited_by = ?) WHERE invitation_code = ?")
                                   ->execute([$agent_code, $agent_code]);
                            }
                            showSuccess('User updated successfully!');
                            if (isAjaxRequest()) {
                                jsonResponse(['success' => true, 'message' => 'User updated successfully!']);
                            } else {
                                redirect('admin/member_management/edit.php?id=' . $user_id . '&action=edit');
                            }
                            exit();
                        } else {
                            showError('Failed to update user.');
                            if (isAjaxRequest()) {
                                jsonResponse(['success' => false, 'message' => 'Failed to update user.']);
                            } else {
                                redirect('admin/member_management/edit.php?id=' . $user_id . '&action=edit');
                            }
                            exit();
                        }
                    default: // Fallback for any other unexpected action
                        showError('Invalid action specified.');
                        break;
                }
                
                exit();
            } catch (Exception $ex) {
                logError('Edit user fatal error: ' . $ex->getMessage(), __FILE__, __LINE__);
                showError('A system error occurred. Please check the error log.');
            }
        }
    }
} catch (Exception $e) {
    logError('Edit user outer error: ' . $e->getMessage(), __FILE__, __LINE__);
    showError($e->getMessage());
}

$vip_levels = fetchAll('SELECT level, name FROM vip_levels ORDER BY level ASC');
$user_statuses = ['pending', 'active', 'suspended', 'banned'];

$page_title = 'Edit Member';
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Edit Member: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to List</a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs">
                            <li class="nav-item"><a class="nav-link <?php if ($action === 'edit') echo 'active'; ?>" href="?id=<?php echo $user_id; ?>&action=edit">Basic Information</a></li>
                            <li class="nav-item"><a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#adjustBalanceModal">Plus Deduction</a></li>
                            <li class="nav-item"><a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#creditScoreModal">Credit Score</a></li>
                            <li class="nav-item"><a class="nav-link <?php if ($action === 'change_password') echo 'active'; ?>" href="?id=<?php echo $user_id; ?>&action=change_password">Change Password</a></li>
                            <li class="nav-item"><a class="nav-link <?php if ($action === 'change_payment_password') echo 'active'; ?>" href="?id=<?php echo $user_id; ?>&action=change_payment_password">Payment Password</a></li>
                            <li class="nav-item"><a class="nav-link <?php if ($action === 'wallet_info') echo 'active'; ?>" href="?id=<?php echo $user_id; ?>&action=wallet_info">Wallet Info</a></li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <?php if ($action === 'edit'): ?>
                            <form action="edit.php?id=<?php echo $user_id; ?>" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Avatar</label>
                                        <div class="avatar-section">
                                            <div class="avatar-display">
                                                <?php if (!empty($user['avatar'])): ?>
                                                    <img src="<?php echo BASE_URL . 'uploads/avatars/' . $user['avatar']; ?>" alt="Avatar" class="avatar-image">
                                                <?php else: ?>
                                                    <div class="avatar-initials">
                                                        <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-primary mt-2" data-bs-toggle="modal" data-bs-target="#avatarModal">
                                                <i class="bi bi-camera me-1"></i>Change Avatar
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="vip_level" class="form-label">VIP Level</label>
                                        <select class="form-select" id="vip_level" name="vip_level">
                                            <?php foreach ($vip_levels as $level): ?>
                                                <option value="<?php echo $level['level']; ?>" <?php echo ($user['vip_level'] == $level['level']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($level['name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <?php foreach ($user_statuses as $status): ?>
                                                <option value="<?php echo $status; ?>" <?php echo ($user['status'] == $status) ? 'selected' : ''; ?>><?php echo ucfirst($status); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="balance" class="form-label">Balance <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="User's available balance for transactions."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo getAppSetting('default_currency', DEFAULT_CURRENCY); ?></span>
                                            <input type="number" class="form-control" id="balance" name="balance" step="0.01" value="<?php echo htmlspecialchars($user['balance']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="frozen_balance" class="form-label">Frozen Balance <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Funds temporarily unavailable (e.g., under review or withdrawal in process)."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo getAppSetting('default_currency', DEFAULT_CURRENCY); ?></span>
                                            <input type="number" class="form-control" id="frozen_balance" name="frozen_balance" step="0.01" value="<?php echo htmlspecialchars($user['frozen_balance']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="commission_balance" class="form-label">Commission Balance <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Earnings from commissions, e.g., from referrals or team activities."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text">USDT</span>
                                            <input type="number" class="form-control" id="commission_balance" name="commission_balance" step="0.01" value="<?php echo htmlspecialchars($user['commission_balance']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="total_profit" class="form-label">Total Profit <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Cumulative profit earned by the user (readonly)."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text">USDT</span>
                                            <input type="number" class="form-control" id="total_profit" name="total_profit" step="0.01" value="<?php echo htmlspecialchars($user['total_profit'] ?? 0); ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="agent_code" class="form-label">Invited By (Agent/Superior)</label>
                                        <select class="form-select" id="agent_code" name="agent_code">
                                            <option value="">-- Select Agent --</option>
                                            <?php foreach ($all_agents as $agent): ?>
                                                <option value="<?php echo htmlspecialchars($agent['invitation_code']); ?>" data-name="<?php echo htmlspecialchars($agent['name']); ?>" data-phone="<?php echo htmlspecialchars($agent['phone']); ?>"
                                                    <?php if (!empty($user['invited_by']) && $user['invited_by'] == $agent['invitation_code']) echo 'selected'; ?>>
                                                    <?php echo htmlspecialchars($agent['name']) . ' (' . $agent['invitation_code'] . ')'; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Invited By (Agent Name)</label>
                                        <input type="text" class="form-control" id="invited_by_name" value="<?php echo htmlspecialchars($user['invited_by_name'] ?? 'N/A'); ?>" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Invitation Code (ID)</label>
                                        <input type="text" class="form-control" id="invited_by_code" value="<?php echo htmlspecialchars($user['invited_by'] ?? 'N/A'); ?>" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Invited By (Phone)</label>
                                        <input type="text" class="form-control" id="invited_by_phone" value="<?php echo htmlspecialchars($user['invited_by_phone'] ?? 'N/A'); ?>" readonly>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary"><i class="bi bi-check-circle me-2"></i>Save Changes</button>
                                </div>
                            </form>
                            <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const agentSelect = document.getElementById('agent_code');
                                if (agentSelect) {
                                    agentSelect.addEventListener('change', function() {
                                        const selected = agentSelect.options[agentSelect.selectedIndex];
                                        document.getElementById('invited_by_name').value = selected.getAttribute('data-name') || 'N/A';
                                        document.getElementById('invited_by_code').value = selected.value || 'N/A';
                                        document.getElementById('invited_by_phone').value = selected.getAttribute('data-phone') || 'N/A';
                                    });
                                    // Trigger change event on page load if agent is already selected
                                    if (agentSelect.value) {
                                        const selected = agentSelect.options[agentSelect.selectedIndex];
                                        document.getElementById('invited_by_name').value = selected.getAttribute('data-name') || 'N/A';
                                        document.getElementById('invited_by_code').value = selected.value || 'N/A';
                                        document.getElementById('invited_by_phone').value = selected.getAttribute('data-phone') || 'N/A';
                                    }
                                }
                            });
                            </script>
                        <?php elseif ($action === 'wallet_info'): ?>
                            <form action="edit.php?id=<?php echo $user_id; ?>&action=wallet_info" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <div class="mb-3">
                                    <label for="usdt_wallet_address" class="form-label">USDT Wallet Address</label>
                                    <input type="text" class="form-control" id="usdt_wallet_address" name="usdt_wallet_address" value="<?php echo htmlspecialchars($user['usdt_wallet_address'] ?? ''); ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="exchange_name" class="form-label">Exchange Name</label>
                                    <input type="text" class="form-control" id="exchange_name" name="exchange_name" value="<?php echo htmlspecialchars($user['exchange_name'] ?? ''); ?>">
                                </div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary"><i class="bi bi-check-circle me-2"></i>Update Wallet Info</button>
                                </div>
                            </form>
                        <?php elseif ($action === 'adjust_balance'): ?>
                            <form action="edit.php?id=<?php echo $user_id; ?>&action=adjust_balance" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <div class="mb-3">
                                    <label for="amount" class="form-label">Amount</label>
                                    <input type="number" class="form-control" id="amount" name="amount" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label for="type" class="form-label">Operation Type</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="addition">Increase Amount</option>
                                        <option value="deduction">Decrease Amount</option>
                                    </select>
                                </div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary"><i class="bi bi-check-circle me-2"></i>Apply Adjustment</button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<!-- Avatar Upload Modal -->
<div class="modal fade" id="avatarModal" tabindex="-1" aria-labelledby="avatarModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="avatarModalLabel">Change Avatar</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="edit.php?id=<?php echo $user_id; ?>&action=upload_avatar" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <div class="mb-3">
                        <label for="avatar" class="form-label">Select Avatar Image</label>
                        <input type="file" class="form-control" id="avatar" name="avatar" accept="image/png,image/jpeg,image/jpg" required onchange="previewAvatar(this)">
                        <div class="form-text">Upload a PNG or JPG image (Max 2MB)</div>
                    </div>
                    <div class="avatar-preview" id="avatarPreview" style="display: none;">
                        <label class="form-label">Preview:</label>
                        <div class="preview-container">
                            <img id="previewImage" src="" alt="Avatar Preview" class="avatar-preview-image">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload Avatar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Adjust Balance Modal -->
<div class="modal fade" id="adjustBalanceModal" tabindex="-1" aria-labelledby="adjustBalanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adjustBalanceModalLabel">Adjust User Balance</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="adjustBalanceForm" action="edit.php?id=<?php echo $user_id; ?>&action=adjust_balance" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="user_id" value="<?php echo $user_id; ?>">
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                    <p><strong>Current Balance:</strong> <span id="currentBalanceDisplay"><?php echo formatCurrency($user['balance']); ?></span></p>
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount</label>
                        <input type="number" class="form-control" id="amount" name="amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="type" class="form-label">Operation Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="addition">Increase Amount</option>
                            <option value="deduction">Decrease Amount</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Apply Adjustment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Credit Score Modal -->
<div class="modal fade" id="creditScoreModal" tabindex="-1" aria-labelledby="creditScoreModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="creditScoreModalLabel">Update Credit Score</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="edit.php?id=<?php echo $user_id; ?>&action=credit_score" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <div class="mb-3">
                        <label for="score" class="form-label">Percentage</label>
                        <input type="number" class="form-control" id="score" name="score" value="<?php echo htmlspecialchars(isset($user['credit_score']) ? $user['credit_score'] : ''); ?>" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>

<style>
/* Avatar Styles */
.avatar-section {
    text-align: center;
}

.avatar-display {
    width: 80px;
    height: 80px;
    margin: 0 auto 10px;
    position: relative;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e9ecef;
    background: #f8f9fa;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: #6c757d;
    background: linear-gradient(135deg, #e9ecef, #f8f9fa);
}

.avatar-preview {
    text-align: center;
    margin-top: 1rem;
}

.preview-container {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e9ecef;
    background: #f8f9fa;
}

.avatar-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Adjust Balance Form Submission (AJAX)
    const adjustBalanceForm = document.getElementById('adjustBalanceForm');
    if (adjustBalanceForm) {
        adjustBalanceForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent default form submission

            const formData = new FormData(adjustBalanceForm);
            
            fetch(adjustBalanceForm.action, {
                method: 'POST',
                body: new URLSearchParams(formData) // Use URLSearchParams for x-www-form-urlencoded
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Success: ' + data.message);
                    // After success, go back to Basic Information tab
                    window.location.href = 'edit.php?id=<?php echo $user_id; ?>&action=edit';
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adjusting balance.');
            })
            .finally(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('adjustBalanceModal'));
                if (modal) {
                    modal.hide();
                }
            });
        });
    }

    // Auto-generate USDT Wallet Address
    const generateWalletBtn = document.getElementById('generateWalletAddress');
    if (generateWalletBtn) {
        generateWalletBtn.addEventListener('click', function() {
            const walletAddressInput = document.getElementById('usdt_wallet_address');
            if (walletAddressInput) {
                walletAddressInput.value = generateRandomWalletAddress();
            }
        });
    }

    function generateRandomWalletAddress() {
        const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '0x'; // Common prefix for Ethereum-based addresses
        for (let i = 0; i < 38; i++) { // Generate 38 more characters for a 40-char address
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }

    // Enable Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Avatar preview function
function previewAvatar(input) {
    const preview = document.getElementById('avatarPreview');
    const previewImage = document.getElementById('previewImage');

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, PNG)');
            input.value = '';
            preview.style.display = 'none';
            return;
        }

        // Validate file size (2MB max)
        if (file.size > 2 * 1024 * 1024) {
            alert('File size must be less than 2MB');
            input.value = '';
            preview.style.display = 'none';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
}
</script>
