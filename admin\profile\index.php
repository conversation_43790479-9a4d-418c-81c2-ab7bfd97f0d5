<?php
/**
 * Bamboo Web Application - Admin Profile
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('../login/');
}

$admin_id = $_SESSION['admin_id'] ?? 0;
if (!$admin_id) {
    showError('Invalid admin session.');
    redirect('../login/');
}

// Fetch admin info
$admin = fetchRow("SELECT * FROM admins WHERE id = ?", [$admin_id]);
if (!$admin) {
    showError('Admin not found.');
    redirect('../login/');
}

$page_title = 'My Profile';
include '../includes/admin_header.php';
?>
<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container py-5">
                <div class="card mx-auto" style="max-width: 500px;">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">My Profile</h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($admin['username']); ?>" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Full Name</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($admin['full_name']); ?>" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" value="<?php echo htmlspecialchars($admin['email']); ?>" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Role</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($admin['role']); ?>" readonly>
                        </div>
                        <a href="edit.php" class="btn btn-primary">Edit Profile</a>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>
<?php include '../includes/admin_footer_scripts.php'; ?>
