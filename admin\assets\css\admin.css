/**
 * Bamboo Web Application - Admin CSS
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== ADMIN ROOT VARIABLES ===== */
:root {
    --admin-primary: #ff6900;
    --admin-secondary: #ffffff;
    --admin-primary-rgb: 255, 105, 0;
    --admin-sidebar-width: 280px;
    --admin-topbar-height: 70px;
    --admin-footer-height: 60px;

    --admin-bg-light: #fafbfc;
    --admin-bg-white: #ffffff;
    --admin-bg-card: #ffffff;
    --admin-text-dark: #1f2937;
    --admin-text-muted: #6b7280;
    --admin-border: rgba(0, 0, 0, 0.06);
    --admin-border-light: rgba(0, 0, 0, 0.04);

    --admin-shadow-xs: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
    --admin-shadow-sm: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    --admin-shadow-md: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
    --admin-shadow-lg: 0 1.5rem 3rem rgba(0, 0, 0, 0.15);
    --admin-shadow-xl: 0 2rem 4rem rgba(0, 0, 0, 0.18);

    --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --admin-border-radius: 1rem;
    --admin-border-radius-sm: 0.75rem;
    --admin-border-radius-lg: 1.25rem;
}

/* ===== GLOBAL ADMIN STYLES ===== */
.admin-page {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--admin-bg-light) 0%, #f1f3f4 100%);
    color: var(--admin-text-dark);
    line-height: 1.6;
    font-weight: 400;
}

/* Enhanced Card System */
.card {
    background: var(--admin-bg-card);
    border: 1px solid var(--admin-border-light);
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-sm);
    transition: var(--admin-transition);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: var(--admin-shadow-md);
    border-color: var(--admin-border);
}

.card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-bottom: 1px solid var(--admin-border-light);
    padding: 1.25rem 1.5rem;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--admin-border), transparent);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid var(--admin-border-light);
    padding: 1rem 1.5rem;
}

/* ===== ADMIN WRAPPER ===== */
.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* ===== ADMIN SIDEBAR ===== */
.admin-sidebar {
    width: var(--admin-sidebar-width);
    background: linear-gradient(180deg, var(--admin-primary) 0%, rgba(var(--admin-primary-rgb), 0.9) 100%);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--admin-transition);
    box-shadow: var(--admin-shadow-md);
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
}

.admin-sidebar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* Sidebar Brand */
.sidebar-brand {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
}

.brand-link:hover {
    color: rgba(255, 255, 255, 0.9);
}

.brand-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 12px;
    object-fit: contain;
}

.brand-logo-svg {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-logo-svg .brand-logo {
    width: 100%;
    height: 100%;
    margin-right: 0;
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1.5rem;
}

.brand-text {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.2;
}

.brand-subtitle {
    display: block;
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 2px;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--admin-secondary);
    text-decoration: none;
    transition: var(--admin-transition);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.sidebar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--admin-secondary);
}

.sidebar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-right: 3px solid white;
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-right: 12px;
    font-size: 1.1rem;
}

.sidebar-nav .nav-link span {
    flex: 1;
}

.sidebar-nav .badge {
    font-size: 0.7rem;
}

.nav-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 1rem 1.5rem;
    list-style: none;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1.5rem;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    opacity: 0.8;
}

.sidebar-actions {
    display: flex;
    gap: 0.5rem;
}

.sidebar-actions .btn {
    flex: 1;
    border-color: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.sidebar-actions .btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* ===== ADMIN MAIN CONTENT ===== */
.admin-main {
    flex: 1;
    margin-left: var(--admin-sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--admin-bg-light) 0%, #f1f3f4 100%);
}

.admin-content {
    flex: 1;
    padding: 0;
    background: transparent;
    min-height: calc(100vh - var(--admin-topbar-height) - var(--admin-footer-height));
    margin-bottom: var(--admin-footer-height);
}

.admin-content .container-fluid {
    padding: 2rem 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - var(--admin-topbar-height) - var(--admin-footer-height) - 4rem);
}

/* ===== ADMIN TOPBAR ===== */
.admin-topbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border-light);
    box-shadow: var(--admin-shadow-xs);
    position: sticky;
    top: 0;
    z-index: 999;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--admin-primary);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--admin-text-muted);
}

/* Notification Dropdown */
.notification-dropdown {
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-dropdown .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.notification-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

/* ===== ADMIN CONTENT LEGACY (REMOVED DUPLICATE) ===== */
/* This rule was causing conflicts - content moved to main admin-content rule above */

/* ===== ADMIN FOOTER ===== */
.admin-footer {
    background: var(--admin-secondary);
    padding: 1rem 0;
    border-top: 1px solid var(--admin-border);
    margin-top: auto;
    height: var(--admin-footer-height);
    position: relative;
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* ===== LOADING SPINNER ===== */
.admin-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.admin-loading.show {
    display: flex;
}

/* ===== FLASH MESSAGES ===== */
.admin-flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 400px;
}

.admin-flash-messages .alert {
    margin-bottom: 10px;
    border: none;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-lg);
}

/* ===== STAT CARDS ===== */
.stat-card {
    border: none;
    border-radius: var(--admin-border-radius);
    box-shadow: 0 0.05rem 0.15rem rgba(0, 0, 0, 0.05);
    transition: var(--admin-transition);
    overflow: hidden;
    background-color: rgba(250, 250, 250, 0.5);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.08);
}

.stat-card-primary {
    border-left: 2px solid var(--admin-primary);
}

.stat-card-success {
    border-left: 2px solid #28a745;
}

.stat-card-info {
    border-left: 2px solid #17a2b8;
}

.stat-card-warning {
    border-left: 2px solid #ffc107;
}

.stat-card-danger {
    border-left: 2px solid #dc3545;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border-radius: var(--admin-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stat-card-danger .stat-icon {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--admin-text-muted);
    margin-bottom: 0.25rem;
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: var(--admin-border-radius);
    box-shadow: 0 0.05rem 0.15rem rgba(0, 0, 0, 0.05);
    transition: var(--admin-transition);
    background-color: rgba(250, 250, 250, 0.5);
}

.card:hover {
    box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.08);
}

.card-header {
    background: rgba(250, 250, 250, 0.7);
    border-bottom: none;
    border-radius: var(--admin-border-radius) var(--admin-border-radius) 0 0 !important;
    padding: 1rem 1.25rem;
}

.card-title {
    color: var(--admin-text-dark);
    font-weight: 600;
}

/* ===== BUTTONS ===== */
.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border: none;
    border-radius: var(--admin-border-radius);
    transition: var(--admin-transition);
}

.btn-primary:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb), 0.9), var(--admin-primary));
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow-md);
}

.btn-outline-primary {
    border-color: var(--admin-primary);
    color: var(--admin-primary);
    border-radius: var(--admin-border-radius);
}

.btn-outline-primary:hover {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
}

/* ===== FORMS ===== */
.form-control {
    border-radius: var(--admin-border-radius);
    border: 1px solid var(--admin-border);
    transition: var(--admin-transition);
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--admin-primary-rgb), 0.25);
}

/* ===== TABLES ===== */
.table {
    border-radius: var(--admin-border-radius);
    overflow: hidden;
}

.table thead th {
    background: var(--admin-bg-white);
    border-bottom: 2px solid var(--admin-border);
    font-weight: 600;
    color: var(--admin-text-dark);
}

/* ===== COMPACT TABLES FOR TOP-UP ORDERS ===== */
.table-compact th, .table-compact td {
    padding: 0.35rem 0.5rem !important;
    font-size: 13px !important;
    vertical-align: middle !important;
}
.table-compact thead th {
    font-size: 13.5px !important;
    font-weight: 600;
}
.table-compact tbody tr {
    border-bottom: 1px solid var(--admin-border);
}
.table-compact {
    background: #fff;
    border-radius: var(--admin-border-radius);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991.98px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-topbar {
        padding: 1rem;
    }
    
    .admin-content {
        padding: 1rem 0;
        min-height: calc(100vh - var(--admin-topbar-height) - var(--admin-footer-height) - 2rem);
    }

    .admin-content .container-fluid {
        min-height: calc(100vh - var(--admin-topbar-height) - var(--admin-footer-height) - 4rem);
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 575.98px) {
    .admin-topbar {
        padding: 0.75rem;
    }
    
    .admin-content .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* ===== UTILITIES ===== */
.text-admin-primary {
    color: var(--admin-primary) !important;
}

.bg-admin-primary {
    background-color: var(--admin-primary) !important;
}

.border-admin-primary {
    border-color: var(--admin-primary) !important;
}

.shadow-admin {
    box-shadow: var(--admin-shadow-md) !important;
}

/* ===== DARK MODE SUPPORT ===== */
/* Disabled dark mode to ensure white background
@media (prefers-color-scheme: dark) {
    :root {
        --admin-bg-light: #1a1a1a;
        --admin-bg-white: #2d2d2d;
        --admin-text-dark: #ffffff;
        --admin-text-muted: #adb5bd;
        --admin-border: #404040;
    }
}
*/

/* ===== ENHANCED PRIMARY COLOR USAGE ===== */
.card-header, .admin-content h1.h3, .admin-content h3, .admin-content h2, .admin-content h4 {
    color: var(--admin-primary);
}
.card-header.bg-admin-primary, .admin-content .bg-admin-primary {
    background: var(--admin-primary) !important;
    color: #fff !important;
}
.btn-primary, .admin-content .btn-primary {
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border: none;
    color: #fff;
}
.btn-primary:hover, .admin-content .btn-primary:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb), 0.9), var(--admin-primary));
    color: #fff;
}