<?php
/**
 * Bamboo Web Application - Admin Appearance Settings
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    showError('You must be logged in to access the admin dashboard.');
    redirect('admin/login/');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Save appearance settings
        $settings = [
            'primary_color' => $_POST['primary_color'] ?? '#ff6900',
            'secondary_color' => $_POST['secondary_color'] ?? '#ffffff',
            'accent_color' => $_POST['accent_color'] ?? '#007bff',
            'gradient_start' => $_POST['gradient_start'] ?? '#ff6900',
            'gradient_end' => $_POST['gradient_end'] ?? '#ff8533',
            'card_background' => $_POST['card_background'] ?? '#ffffff',
            'sidebar_style' => $_POST['sidebar_style'] ?? 'gradient',
            'card_shadow' => $_POST['card_shadow'] ?? 'subtle',
            'border_radius' => $_POST['border_radius'] ?? '0.5rem',
            'theme_mode' => $_POST['theme_mode'] ?? 'light'
        ];
        
        foreach ($settings as $key => $value) {
            updateAppSetting('appearance_' . $key, $value);
        }
        
        showSuccess('Appearance settings updated successfully!');
        redirect('admin/appearance/');
        
    } catch (Exception $e) {
        showError('Failed to update appearance settings: ' . $e->getMessage());
    }
}

// Get current appearance settings
$current_settings = [
    'primary_color' => getAppSetting('appearance_primary_color', '#ff6900'),
    'secondary_color' => getAppSetting('appearance_secondary_color', '#ffffff'),
    'accent_color' => getAppSetting('appearance_accent_color', '#007bff'),
    'gradient_start' => getAppSetting('appearance_gradient_start', '#ff6900'),
    'gradient_end' => getAppSetting('appearance_gradient_end', '#ff8533'),
    'card_background' => getAppSetting('appearance_card_background', '#ffffff'),
    'sidebar_style' => getAppSetting('appearance_sidebar_style', 'gradient'),
    'card_shadow' => getAppSetting('appearance_card_shadow', 'subtle'),
    'border_radius' => getAppSetting('appearance_border_radius', '0.5rem'),
    'theme_mode' => getAppSetting('appearance_theme_mode', 'light')
];

// Page configuration
$page_title = 'Appearance Settings';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css',
    BASE_URL . 'admin/appearance/appearance.css'
];

// Include admin header
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <!-- Sidebar -->
    <?php include '../includes/admin_sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <?php include '../includes/admin_topbar.php'; ?>
        
        <!-- Content Area -->
        <div class="admin-content">
            <div class="container-fluid">
                
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Appearance Settings</h1>
                        <p class="text-muted">Customize the look and feel of your admin dashboard</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary" id="resetToDefault">
                            <i class="bi bi-arrow-clockwise me-2"></i>Reset to Default
                        </button>
                    </div>
                </div>
                
                <form method="POST" id="appearanceForm">
                    <div class="row">
                        <!-- Color Settings -->
                        <div class="col-lg-6 mb-4">
                            <div class="card appearance-card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-palette me-2"></i>Color Settings
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="primary_color" class="form-label">Primary Color</label>
                                            <div class="color-input-group">
                                                <input type="color" class="form-control form-control-color" 
                                                       id="primary_color" name="primary_color" 
                                                       value="<?php echo htmlspecialchars($current_settings['primary_color']); ?>">
                                                <input type="text" class="form-control color-text" 
                                                       value="<?php echo htmlspecialchars($current_settings['primary_color']); ?>" 
                                                       data-color-input="primary_color">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="secondary_color" class="form-label">Secondary Color</label>
                                            <div class="color-input-group">
                                                <input type="color" class="form-control form-control-color" 
                                                       id="secondary_color" name="secondary_color" 
                                                       value="<?php echo htmlspecialchars($current_settings['secondary_color']); ?>">
                                                <input type="text" class="form-control color-text" 
                                                       value="<?php echo htmlspecialchars($current_settings['secondary_color']); ?>" 
                                                       data-color-input="secondary_color">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="accent_color" class="form-label">Accent Color</label>
                                            <div class="color-input-group">
                                                <input type="color" class="form-control form-control-color" 
                                                       id="accent_color" name="accent_color" 
                                                       value="<?php echo htmlspecialchars($current_settings['accent_color']); ?>">
                                                <input type="text" class="form-control color-text" 
                                                       value="<?php echo htmlspecialchars($current_settings['accent_color']); ?>" 
                                                       data-color-input="accent_color">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="card_background" class="form-label">Card Background</label>
                                            <div class="color-input-group">
                                                <input type="color" class="form-control form-control-color" 
                                                       id="card_background" name="card_background" 
                                                       value="<?php echo htmlspecialchars($current_settings['card_background']); ?>">
                                                <input type="text" class="form-control color-text" 
                                                       value="<?php echo htmlspecialchars($current_settings['card_background']); ?>" 
                                                       data-color-input="card_background">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Gradient Settings -->
                        <div class="col-lg-6 mb-4">
                            <div class="card appearance-card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-rainbow me-2"></i>Gradient Settings
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="gradient_start" class="form-label">Gradient Start</label>
                                            <div class="color-input-group">
                                                <input type="color" class="form-control form-control-color" 
                                                       id="gradient_start" name="gradient_start" 
                                                       value="<?php echo htmlspecialchars($current_settings['gradient_start']); ?>">
                                                <input type="text" class="form-control color-text" 
                                                       value="<?php echo htmlspecialchars($current_settings['gradient_start']); ?>" 
                                                       data-color-input="gradient_start">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="gradient_end" class="form-label">Gradient End</label>
                                            <div class="color-input-group">
                                                <input type="color" class="form-control form-control-color" 
                                                       id="gradient_end" name="gradient_end" 
                                                       value="<?php echo htmlspecialchars($current_settings['gradient_end']); ?>">
                                                <input type="text" class="form-control color-text" 
                                                       value="<?php echo htmlspecialchars($current_settings['gradient_end']); ?>" 
                                                       data-color-input="gradient_end">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Gradient Preview -->
                                    <div class="gradient-preview" id="gradientPreview">
                                        <span>Gradient Preview</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Style Settings -->
                        <div class="col-lg-6 mb-4">
                            <div class="card appearance-card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-brush me-2"></i>Style Settings
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="sidebar_style" class="form-label">Sidebar Style</label>
                                        <select class="form-select" id="sidebar_style" name="sidebar_style">
                                            <option value="gradient" <?php echo $current_settings['sidebar_style'] === 'gradient' ? 'selected' : ''; ?>>Gradient</option>
                                            <option value="solid" <?php echo $current_settings['sidebar_style'] === 'solid' ? 'selected' : ''; ?>>Solid Color</option>
                                            <option value="light" <?php echo $current_settings['sidebar_style'] === 'light' ? 'selected' : ''; ?>>Light Theme</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="card_shadow" class="form-label">Card Shadow</label>
                                        <select class="form-select" id="card_shadow" name="card_shadow">
                                            <option value="none" <?php echo $current_settings['card_shadow'] === 'none' ? 'selected' : ''; ?>>No Shadow</option>
                                            <option value="subtle" <?php echo $current_settings['card_shadow'] === 'subtle' ? 'selected' : ''; ?>>Subtle</option>
                                            <option value="medium" <?php echo $current_settings['card_shadow'] === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                            <option value="strong" <?php echo $current_settings['card_shadow'] === 'strong' ? 'selected' : ''; ?>>Strong</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="border_radius" class="form-label">Border Radius</label>
                                        <select class="form-select" id="border_radius" name="border_radius">
                                            <option value="0" <?php echo $current_settings['border_radius'] === '0' ? 'selected' : ''; ?>>Sharp (0px)</option>
                                            <option value="0.25rem" <?php echo $current_settings['border_radius'] === '0.25rem' ? 'selected' : ''; ?>>Small (4px)</option>
                                            <option value="0.5rem" <?php echo $current_settings['border_radius'] === '0.5rem' ? 'selected' : ''; ?>>Medium (8px)</option>
                                            <option value="0.75rem" <?php echo $current_settings['border_radius'] === '0.75rem' ? 'selected' : ''; ?>>Large (12px)</option>
                                            <option value="1rem" <?php echo $current_settings['border_radius'] === '1rem' ? 'selected' : ''; ?>>Extra Large (16px)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="theme_mode" class="form-label">Theme Mode</label>
                                        <select class="form-select" id="theme_mode" name="theme_mode">
                                            <option value="light" <?php echo $current_settings['theme_mode'] === 'light' ? 'selected' : ''; ?>>Light</option>
                                            <option value="dark" <?php echo $current_settings['theme_mode'] === 'dark' ? 'selected' : ''; ?>>Dark</option>
                                            <option value="auto" <?php echo $current_settings['theme_mode'] === 'auto' ? 'selected' : ''; ?>>Auto</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Live Preview -->
                        <div class="col-lg-6 mb-4">
                            <div class="card appearance-card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-eye me-2"></i>Live Preview
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="preview-container">
                                        <!-- Mini Dashboard Preview -->
                                        <div class="mini-dashboard" id="previewDashboard">
                                            <div class="mini-sidebar" id="previewSidebar">
                                                <div class="mini-brand">Dashboard</div>
                                                <div class="mini-nav">
                                                    <div class="mini-nav-item active">Home</div>
                                                    <div class="mini-nav-item">Settings</div>
                                                    <div class="mini-nav-item">Users</div>
                                                </div>
                                            </div>
                                            <div class="mini-content">
                                                <div class="mini-card" id="previewCard1">
                                                    <div class="mini-card-header">Statistics</div>
                                                    <div class="mini-card-body">Sample content</div>
                                                </div>
                                                <div class="mini-card" id="previewCard2">
                                                    <div class="mini-card-header">Chart</div>
                                                    <div class="mini-card-body">Chart area</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Save Button -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card appearance-card">
                                <div class="card-body text-center">
                                    <button type="submit" class="btn btn-primary btn-lg me-3">
                                        <i class="bi bi-check-circle me-2"></i>Save Appearance Settings
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg" id="previewChanges">
                                        <i class="bi bi-eye me-2"></i>Preview Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                
            </div>
        </div>
        
        <!-- Footer -->
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js',
    BASE_URL . 'admin/appearance/appearance.js'
];
include '../includes/admin_footer_scripts.php';
?>
