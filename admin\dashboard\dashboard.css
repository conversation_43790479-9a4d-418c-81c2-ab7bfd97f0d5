/**
 * Bamboo Web Application - Admin Dashboard Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== DASHBOARD SPECIFIC STYLES ===== */

/* Statistics Cards */
.stat-card {
    position: relative;
    overflow: hidden;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 12px rgba(0,0,0,0.15);
}

.stat-card::before {
    display: none !important;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stat-card-danger .stat-icon {
    background: linear-gradient(135deg, #dc3545, #bd2130);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--admin-text-muted);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

/* Dashboard Cards */
.card {
    border: none;
    border-radius: var(--admin-border-radius);
    box-shadow: var(--admin-shadow-sm);
    transition: var(--admin-transition);
}

.card:hover {
    box-shadow: var(--admin-shadow-md);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--admin-border);
    border-radius: var(--admin-border-radius) var(--admin-border-radius) 0 0 !important;
}

.card-title {
    color: var(--admin-text-dark);
    font-weight: 600;
}

/* List Groups */
.list-group-item {
    border: none;
    border-bottom: 1px solid #f1f3f4;
    padding: 1rem;
    transition: var(--admin-transition);
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Quick Actions */
.btn {
    border-radius: var(--admin-border-radius);
    font-weight: 500;
    transition: var(--admin-transition);
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border-color: #28a745;
}

.btn-outline-info:hover {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
    border-color: #17a2b8;
}

.btn-outline-warning:hover {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border-color: #ffc107;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Page Header */
.admin-content h1 {
    color: var(--admin-text-dark);
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .list-group-item {
        padding: 0.75rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Dashboard Animations */
.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--admin-text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Status Indicators */
.status-online {
    color: #28a745;
}

.status-offline {
    color: #dc3545;
}

.status-warning {
    color: #ffc107;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Custom Scrollbar for Dashboard */
.dashboard-scroll::-webkit-scrollbar {
    width: 6px;
}

.dashboard-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dashboard-scroll::-webkit-scrollbar-thumb {
    background: var(--admin-primary);
    border-radius: 3px;
}

.dashboard-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--admin-primary-rgb), 0.8);
}