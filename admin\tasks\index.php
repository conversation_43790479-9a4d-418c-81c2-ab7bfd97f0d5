<?php
/**
 * Bamboo Web Application - Task Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$page_title = 'Task Management';
include '../includes/admin_header.php';

// Pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Search and Filter
$search_term = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');

$where_clause = '1=1';
$params = [];

if (!empty($search_term)) {
    $where_clause .= ' AND (u.username LIKE ? OR p.name LIKE ?)';
    $params = array_merge($params, ["%$search_term%", "%$search_term%"]);
}

if (!empty($status_filter)) {
    $where_clause .= ' AND t.status = ?';
    $params[] = $status_filter;
}

$total_tasks = getRecordCount('tasks t LEFT JOIN users u ON t.user_id = u.id LEFT JOIN products p ON t.product_id = p.id', $where_clause, $params);
$total_pages = ceil($total_tasks / $limit);

$query = "SELECT t.*, u.username, p.name as product_name 
          FROM tasks t 
          LEFT JOIN users u ON t.user_id = u.id
          LEFT JOIN products p ON t.product_id = p.id
          WHERE $where_clause
          ORDER BY t.assigned_at DESC
          LIMIT $limit OFFSET $offset";

$tasks = fetchAll($query, $params);
$statuses = ['assigned', 'in_progress', 'completed', 'failed', 'expired'];

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Task Management</h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">All Tasks (<?php echo $total_tasks; ?>)</h5>
                        <form action="" method="GET" class="d-flex mt-3">
                            <input type="text" name="search" class="form-control me-2" placeholder="Search by user or product..." value="<?php echo htmlspecialchars($search_term); ?>">
                            <select name="status" class="form-select me-2">
                                <option value="">All Statuses</option>
                                <?php foreach ($statuses as $status): ?>
                                    <option value="<?php echo $status; ?>" <?php echo ($status_filter == $status) ? 'selected' : ''; ?>><?php echo ucfirst($status); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="btn btn-primary"><i class="bi bi-search"></i></button>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>S/N</th>
                                        <th>User</th>
                                        <th>Product</th>
                                        <th>Commission</th>
                                        <th>Status</th>
                                        <th>Assigned At</th>
                                        <th>Completed At</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($tasks)): ?>
                                        <?php
                                        // Ensure proper numbering calculation
                                        $current_page = max(1, (int)$page);
                                        $items_per_page = max(1, (int)$per_page);
                                        $sn = ($current_page - 1) * $items_per_page + 1;
                                        ?>
                                        <?php foreach ($tasks as $task): ?>
                                        <tr>
                                            <td><div class="serial-number"><?php echo $sn++; ?></div></td>
                                            <td><?php echo htmlspecialchars($task['username']); ?></td>
                                            <td><?php echo htmlspecialchars($task['product_name']); ?></td>
                                            <td><?php echo formatCurrency($task['commission_earned']); ?></td>
                                            <td><span class="badge bg-<?php echo getStatusBadgeClass($task['status']); ?>"><?php echo ucfirst($task['status']); ?></span></td>
                                            <td><?php echo formatDate($task['assigned_at']); ?></td>
                                            <td><?php echo $task['completed_at'] ? formatDate($task['completed_at']) : 'N/A'; ?></td>
                                        </tr>
                                    <?php endforeach; else: ?>
                                        <tr><td colspan="7" class="text-center text-muted">No tasks found.</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php renderPagination($page, $total_pages, 'search=' . urlencode($search_term) . '&status=' . urlencode($status_filter)); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
