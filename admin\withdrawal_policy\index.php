<?php
/**
 * Bamboo Web Application - Withdrawal Policy
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $settings_to_update = [
            'withdrawal_policy_text' => sanitizeInput($_POST['withdrawal_policy_text'] ?? ''),
        ];

        foreach ($settings_to_update as $key => $value) {
            updateSetting($key, $value);
        }

        showSuccess('Withdrawal policy updated successfully!');
        redirect('admin/withdrawal_policy/');
        exit();
    }
}

$settings = getSettings('withdrawal_policy');
$page_title = 'Withdrawal Policy';
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Withdrawal Policy</h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Edit Withdrawal Policy</h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-3">
                                <label for="withdrawal_policy_text" class="form-label">Policy Text</label>
                                <textarea class="form-control" id="withdrawal_policy_text" name="withdrawal_policy_text" rows="10"><?php echo htmlspecialchars($settings['withdrawal_policy_text'] ?? ''); ?></textarea>
                                <div class="form-text">The text of the withdrawal policy. This will be displayed to users.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Policy</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
