<?php
/**
 * Bamboo Web Application - Admin Payment Card Page
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$page_title = 'Payment Card Management';
$page_section = 'payment_card';

// Fetch all users with wallet information
$query = "SELECT u.id, u.username, u.email, u.phone, u.usdt_wallet_address, u.exchange_name, u.updated_at as binding_time
          FROM users u
          WHERE u.usdt_wallet_address IS NOT NULL AND u.usdt_wallet_address != ''
          ORDER BY u.id DESC";
$users_with_wallets = fetchAll($query);

include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/admin_sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><?php echo $page_title; ?></h1>
            </div>

            <div class="card">
                <div class="card-header">
                    User Wallet Addresses
                </div>
                <div class="card-body">
                    <?php if (empty($users_with_wallets)): ?>
                        <p>No user wallet addresses found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>User ID</th>
                                        <th>Username</th>
                                        <th>Exchange</th>
                                        <th>Wallet Address</th>
                                        <th>Binding Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users_with_wallets as $user): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($user['id']); ?></td>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['exchange_name'] ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($user['usdt_wallet_address']); ?></td>
                                            <td><?php echo formatDate($user['binding_time']); ?></td>
                                            <td>
                                                <a href="../member_management/view.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-info" title="View User">
                                                    <i class="bi bi-eye"></i> View User
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        </main>
    </div>
</div>

<?php include '../includes/admin_footer.php'; ?>
