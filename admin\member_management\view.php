<?php
/**
 * Bamboo Web Application - View Member
 * Company: Notepadsly
 * Version: 1.1
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

// Fetch user info
$query = "SELECT u.*, v.name as vip_name FROM users u LEFT JOIN vip_levels v ON u.vip_level = v.level WHERE u.id = ?";
$user = fetchRow($query, [$user_id]);

if (!$user) {
    redirect('admin/member_management/');
}

// Fetch inviter (agent/superior) info from superiors table using invited_by (invitation code)
$inviter = null;
if (!empty($user['invited_by'])) {
    $inviter = fetchRow("SELECT * FROM superiors WHERE invitation_code = ?", [$user['invited_by']]);
}

// Referral count: number of users whose invited_by matches this user's invitation_code
$referral_count = fetchRow("SELECT COUNT(*) as cnt FROM users WHERE invited_by = ?", [$user['invitation_code']]);
$referral_count = $referral_count ? (int)$referral_count['cnt'] : 0;

$page_title = 'View Member';
$additional_css = [BASE_URL . 'admin/assets/css/member-details.css'];
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">User Details: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <div>
                        <a href="edit.php?id=<?php echo $user_id; ?>" class="btn btn-primary"><i class="bi bi-pencil me-2"></i>Edit User</a>
                        <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to List</a>
                    </div>
                </div>

                <!-- Quick Actions Section - Prominently Displayed -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card quick-actions-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning-charge me-2"></i>Quick Actions
                                </h5>
                                <small class="opacity-75">Manage user account and settings</small>
                            </div>
                            <div class="card-body p-0">
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <a href="#" class="quick-action-item" data-bs-toggle="modal" data-bs-target="#adjustBalanceModal">
                                            <div class="quick-action-icon bg-success">
                                                <i class="bi bi-plus-circle"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Plus Deduction</div>
                                                <div class="quick-action-desc">Adjust balance</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="negative_settings.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-warning">
                                                <i class="bi bi-exclamation-triangle"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Negative Settings</div>
                                                <div class="quick-action-desc">Configure limits</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="withdraw_quotes.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-info">
                                                <i class="bi bi-cash-coin"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Withdraw Quote</div>
                                                <div class="quick-action-desc">Manage withdrawals</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="payment_card.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-primary">
                                                <i class="bi bi-credit-card"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Payment Card</div>
                                                <div class="quick-action-desc">Card management</div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <a href="edit.php?id=<?php echo $user_id; ?>&action=edit" class="quick-action-item">
                                            <div class="quick-action-icon bg-secondary">
                                                <i class="bi bi-person-gear"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Basic Information</div>
                                                <div class="quick-action-desc">Edit profile</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="edit.php?id=<?php echo $user_id; ?>&action=credit_score" class="quick-action-item">
                                            <div class="quick-action-icon bg-dark">
                                                <i class="bi bi-graph-up"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Credit Score</div>
                                                <div class="quick-action-desc">Manage rating</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="salary.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-success">
                                                <i class="bi bi-currency-dollar"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Salary</div>
                                                <div class="quick-action-desc">Salary management</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="reset_task.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-warning">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Reset Task</div>
                                                <div class="quick-action-desc">Task management</div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <a href="edit.php?id=<?php echo $user_id; ?>&action=change_password" class="quick-action-item">
                                            <div class="quick-action-icon bg-info">
                                                <i class="bi bi-key"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Login Password</div>
                                                <div class="quick-action-desc">Change password</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="edit.php?id=<?php echo $user_id; ?>&action=change_payment_password" class="quick-action-item">
                                            <div class="quick-action-icon bg-primary">
                                                <i class="bi bi-shield-lock"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Payment Password</div>
                                                <div class="quick-action-desc">Security settings</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="my_team.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-secondary">
                                                <i class="bi bi-people"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">My Team</div>
                                                <div class="quick-action-desc">Team overview</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="delete.php?id=<?php echo $user_id; ?>" class="quick-action-item danger">
                                            <div class="quick-action-icon bg-danger">
                                                <i class="bi bi-trash"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Delete Account</div>
                                                <div class="quick-action-desc">Permanent removal</div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-circle me-2"></i>Account Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="info-row">
                                    <span class="info-label">Username:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['username']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Email:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['email']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Phone:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['phone']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Gender:</span>
                                    <span class="info-value"><?php echo ucfirst($user['gender']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Status:</span>
                                    <span class="info-value"><span class="badge bg-<?php echo getStatusBadgeClass($user['status']); ?>"><?php echo ucfirst($user['status']); ?></span></span>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-wallet2 me-2"></i>Financial Overview
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Main Balance</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['balance']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Commission Balance</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['commission_balance']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Frozen Balance</div>
                                            <div class="financial-value text-warning fw-bold"><?php echo formatCurrency($user['frozen_balance']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Total Profit</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['total_profit'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Total Deposited</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['total_deposited']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Total Withdrawn</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['total_withdrawn']); ?></div>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <div class="info-row">
                                    <span class="info-label">USDT Wallet Address:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['usdt_wallet_address'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Exchange Name:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['exchange_name'] ?? 'N/A'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">VIP & Referral</h5>
                            </div>
                            <div class="card-body">
                                <div class="info-group">
                                    <span class="info-label">VIP Level:</span>
                                    <span class="info-value badge bg-info fs-6"><?php echo htmlspecialchars($user['vip_name'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Invitation Code:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['invitation_code']); ?></span>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Invited By (Agent Name):</span>
                                    <span class="info-value"><?php echo $inviter ? htmlspecialchars($inviter['name']) : 'N/A'; ?></span>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Referral Count:</span>
                                    <span class="info-value"><?php echo $referral_count; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Activity</h5>
                            </div>
                            <div class="card-body">
                                <div class="info-group">
                                    <span class="info-label">Registration Date:</span>
                                    <small class="info-value"><?php echo formatDate($user['created_at']); ?></small>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Last Login:</span>
                                    <small class="info-value"><?php echo $user['last_login'] ? formatDate($user['last_login']) : 'Never'; ?></small>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Tasks Completed Today:</span>
                                    <small class="info-value"><?php echo $user['tasks_completed_today']; ?></small>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>

<!-- Adjust Balance Modal -->
<div class="modal fade" id="adjustBalanceModal" tabindex="-1" aria-labelledby="adjustBalanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adjustBalanceModalLabel">Adjust User Balance</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="edit.php?id=<?php echo $user_id; ?>&action=adjust_balance" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                    <p><strong>Current Balance:</strong> <?php echo formatCurrency($user['balance']); ?></p>
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount</label>
                        <input type="number" class="form-control" id="amount" name="amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="type" class="form-label">Operation Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="addition">Increase Amount</option>
                            <option value="deduction">Decrease Amount</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Apply Adjustment</button>
                </div>
            </form>
        </div>
    </div>
</div>
