<?php
/**
 * Bamboo Web Application - Salary Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, balance FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

$page_title = 'Salary Management for ' . htmlspecialchars($user['username']);
include '../includes/admin_header.php';

// Handle form submission for paying salary
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $amount = (float)($_POST['amount'] ?? 0);
        $notes = sanitizeInput($_POST['notes']);

        if ($amount > 0) {
            insertRecord('user_salaries', [
                'user_id' => $user_id,
                'amount' => $amount,
                'notes' => $notes,
                'status' => 'paid', // Or 'pending_approval'
                'admin_id_processed' => $_SESSION['admin_id']
            ]);
            // Also adjust user balance
            adjustUserBalance($user_id, $amount, 'addition');
            showSuccess('Salary paid successfully!');
        } else {
            showError('Invalid amount.');
        }
        redirect('admin/member_management/salary.php?id=' . $user_id);
        exit();
    }
}

$salaries = fetchAll('SELECT * FROM user_salaries WHERE user_id = ? ORDER BY created_at DESC', [$user_id]);

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Salary Management for: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">User Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                        <p><strong>Wallet Balance:</strong> <?php echo formatCurrency($user['balance']); ?></p>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Pay Salary</h5>
                    </div>
                    <div class="card-body">
                        <form action="salary.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">Amount</label>
                                    <input type="number" class="form-control" id="amount" name="amount" step="0.01" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Pay Salary</button>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Salary History</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($salaries)): ?>
                            <p class="text-muted">No salary records found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>S/N</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Notes</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $sn = 1; foreach ($salaries as $salary): ?>
                                            <tr>
                                                <td><?php echo $sn++; ?></td>
                                                <td><?php echo formatCurrency($salary['amount']); ?></td>
                                                <td><?php echo ucfirst($salary['status']); ?></td>
                                                <td><?php echo htmlspecialchars($salary['notes']); ?></td>
                                                <td><?php echo formatDate($salary['created_at']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
