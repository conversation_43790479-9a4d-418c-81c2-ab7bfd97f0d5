/**
 * Bamboo Web Application - System Settings Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== SYSTEM SETTINGS STYLES ===== */

/* Settings Cards */
.settings-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    overflow: hidden;
    margin-bottom: 2rem;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
}

.settings-card .card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1.25rem 1.5rem;
    position: relative;
}

.settings-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.settings-card .card-body {
    padding: 1.5rem;
}

.settings-card .card-title {
    color: var(--admin-text-dark);
    font-weight: 600;
    margin-bottom: 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.settings-card .card-title i {
    color: var(--admin-primary);
    margin-right: 0.5rem;
}

/* Form Controls Enhancement */
.form-label {
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.form-control, .form-select {
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--admin-primary-rgb), 0.25);
    transform: scale(1.02);
    background: #ffffff;
}

.form-control:hover, .form-select:hover {
    border-color: rgba(var(--admin-primary-rgb), 0.3);
    background: #ffffff;
}

/* Logo Upload Container */
.logo-upload-container {
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    transition: all 0.3s ease;
}

.logo-upload-container:hover {
    border-color: rgba(var(--admin-primary-rgb), 0.3);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.logo-preview {
    margin-bottom: 1rem;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.logo-preview .preview-image {
    max-width: 200px;
    max-height: 100px;
    object-fit: contain;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.logo-preview .preview-placeholder {
    color: var(--admin-text-muted);
    text-align: center;
}

.logo-preview .preview-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
    opacity: 0.5;
}

.logo-preview .preview-placeholder span {
    font-size: 0.875rem;
    font-weight: 500;
}

/* File Upload Styling */
.form-control[type="file"] {
    padding: 0.5rem;
    border-style: dashed;
    border-width: 2px;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

.form-control[type="file"]:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: var(--admin-primary);
}

/* Form Text */
.form-text {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    margin-top: 0.25rem;
}

/* Textarea Enhancement */
textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* Switch/Checkbox Enhancement */
.form-check {
    padding: 1rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    border-radius: 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.04);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.form-check:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: rgba(var(--admin-primary-rgb), 0.2);
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
    border: 2px solid #dee2e6;
    border-radius: 0.375rem;
}

.form-check-input:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.form-check-label {
    font-weight: 500;
    color: var(--admin-text-dark);
    margin-left: 0.5rem;
}

/* Button Enhancements */
.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, rgba(var(--admin-primary-rgb), 0.8) 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.875rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(var(--admin-primary-rgb), 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.875rem 2rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 0.25rem 0.75rem rgba(108, 117, 125, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(108, 117, 125, 0.4);
}

/* Section Headers */
.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.04);
}

.section-header h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--admin-text-dark), rgba(44, 62, 80, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--admin-text-muted);
    margin-bottom: 0;
}

/* Input Groups */
.input-group-enhanced {
    margin-bottom: 1.5rem;
}

.input-group-enhanced .form-label {
    margin-bottom: 0.75rem;
}

/* Color Picker Enhancement */
.color-picker-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.color-picker-group .form-control[type="color"] {
    width: 60px;
    height: 45px;
    padding: 0.25rem;
    border-radius: 0.5rem;
    cursor: pointer;
}

.color-picker-group .form-control[type="text"] {
    flex: 1;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Form Actions */
.form-actions {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    margin-top: 2rem;
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.form-actions .btn {
    margin: 0 0.5rem;
    min-width: 150px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .settings-card .card-body {
        padding: 1rem;
    }
    
    .form-actions {
        padding: 1.5rem;
    }
    
    .form-actions .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
    
    .color-picker-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .color-picker-group .form-control[type="color"] {
        width: 100%;
        height: 50px;
    }
}

/* Loading States */
.form-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success States */
.form-success {
    border-color: #28a745 !important;
    background: rgba(40, 167, 69, 0.05) !important;
}

.success-icon {
    color: #28a745;
    font-size: 1.25rem;
    margin-left: 0.5rem;
}

/* Validation States */
.is-invalid {
    border-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.05) !important;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

/* Tooltips */
.tooltip-trigger {
    cursor: help;
    color: var(--admin-text-muted);
    margin-left: 0.25rem;
}

.tooltip-trigger:hover {
    color: var(--admin-primary);
}
