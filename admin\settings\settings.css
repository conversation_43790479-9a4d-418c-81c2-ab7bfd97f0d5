/**
 * Bamboo Web Application - System Settings Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== SYSTEM SETTINGS STYLES ===== */

/* Enhanced Page Header */
.settings-page-header {
    background: linear-gradient(135deg, var(--admin-primary) 0%, rgba(var(--admin-primary-rgb), 0.8) 100%);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 0.5rem 2rem rgba(var(--admin-primary-rgb), 0.3);
    border: none;
    position: relative;
    overflow: hidden;
    color: white;
}

.settings-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.6), var(--admin-primary));
}

.header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 0.5rem 1rem rgba(var(--admin-primary-rgb), 0.3);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: white !important;
    margin: 0;
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.8) !important;
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
}

.header-actions {
    margin-left: auto;
}

/* Enhanced Settings Form */
.settings-form {
    position: relative;
}

/* Settings Sections - Full Width */
.settings-section {
    width: 100%;
    margin-bottom: 2rem;
}

/* Settings Cards */
.settings-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    overflow: hidden;
    width: 100%;
    position: relative;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
}

.settings-card .card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    position: relative;
    width: 100%;
}

.settings-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.6), var(--admin-primary));
    border-radius: 1rem 1rem 0 0;
}

/* Enhanced Card Header Content */
.card-header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.3);
}

.card-title-group .card-title {
    color: var(--admin-text-dark);
    font-weight: 600;
    margin: 0;
    font-size: 1.2rem;
}

.card-title-group .card-subtitle {
    color: var(--admin-text-muted);
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.settings-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.settings-card .card-body {
    padding: 1.5rem;
    background: #ffffff;
    width: 100%;
}

/* Full Width Form Elements */
.settings-card .form-control,
.settings-card .form-select {
    width: 100%;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: rgba(248, 249, 250, 0.5);
}

.settings-card .form-control:focus,
.settings-card .form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--admin-primary-rgb), 0.15);
    background: #ffffff;
}

/* Enhanced Row Layout for Full Width */
.settings-card .row {
    margin: 0;
    width: 100%;
}

.settings-card .row .col-md-6,
.settings-card .row .col-md-4,
.settings-card .row .col-md-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Textarea Enhancements */
.settings-card textarea.form-control {
    min-height: 120px;
    resize: vertical;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Enhanced Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group .form-label {
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
}

.form-group .form-label i {
    color: var(--admin-primary);
    font-size: 1rem;
}

/* Enhanced Input Groups */
.input-group-text {
    background: rgba(var(--admin-primary-rgb), 0.1);
    border-color: rgba(0, 0, 0, 0.08);
    color: var(--admin-primary);
    font-weight: 500;
}

.form-text {
    color: var(--admin-text-muted);
    font-size: 0.8rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
}

.form-text i {
    color: var(--admin-primary);
    margin-right: 0.25rem;
}

/* Form Grid Layout */
.form-grid {
    display: grid;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr 1fr;
    }

    .form-grid .form-group.full-width {
        grid-column: 1 / -1;
    }
}

.settings-card .card-title {
    color: var(--admin-text-dark);
    font-weight: 600;
    margin-bottom: 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.settings-card .card-title i {
    color: var(--admin-primary);
    margin-right: 0.5rem;
}

/* Form Controls Enhancement */
.form-label {
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.form-control, .form-select {
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--admin-primary-rgb), 0.25);
    transform: scale(1.02);
    background: #ffffff;
}

.form-control:hover, .form-select:hover {
    border-color: rgba(var(--admin-primary-rgb), 0.3);
    background: #ffffff;
}

/* Enhanced Logo Upload Container */
.logo-upload-container {
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.logo-upload-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(var(--admin-primary-rgb), 0.1), transparent);
    transition: left 0.5s ease;
}

.logo-upload-container:hover::before {
    left: 100%;
}

.logo-upload-container:hover {
    border-color: rgba(var(--admin-primary-rgb), 0.4);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

/* Logo Preview Wrapper */
.logo-preview-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logo-preview-wrapper:hover {
    transform: translateY(-2px);
}

.logo-preview-wrapper:hover .upload-overlay {
    opacity: 1;
}

/* Upload Overlay */
.upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(var(--admin-primary-rgb), 0.9);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    font-weight: 600;
}

.upload-overlay i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.logo-preview {
    margin-bottom: 1rem;
    min-height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.logo-preview:hover {
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.12);
}

.logo-preview .preview-image {
    max-width: 220px;
    max-height: 120px;
    object-fit: contain;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.logo-preview .preview-image:hover {
    transform: scale(1.05);
}

/* SVG Preview Support */
.svg-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.preview-svg {
    max-width: 220px;
    max-height: 120px;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.preview-svg:hover {
    transform: scale(1.05);
}

.logo-preview .preview-placeholder {
    color: var(--admin-text-muted);
    text-align: center;
    padding: 2rem;
}

.logo-preview .preview-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    opacity: 0.6;
    color: var(--admin-primary);
}

.logo-preview .preview-placeholder span {
    font-size: 1rem;
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
}

.logo-preview .preview-placeholder small {
    font-size: 0.8rem;
    opacity: 0.7;
    display: block;
}

/* Hidden File Input */
.logo-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    overflow: hidden;
}

/* File Upload Styling */
.form-control[type="file"] {
    padding: 0.5rem;
    border-style: dashed;
    border-width: 2px;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

.form-control[type="file"]:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: var(--admin-primary);
}

/* Form Text */
.form-text {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    margin-top: 0.25rem;
}

/* Textarea Enhancement */
textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* Switch/Checkbox Enhancement */
.form-check {
    padding: 1rem;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    border-radius: 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.04);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.form-check:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: rgba(var(--admin-primary-rgb), 0.2);
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
    border: 2px solid #dee2e6;
    border-radius: 0.375rem;
}

.form-check-input:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.form-check-label {
    font-weight: 500;
    color: var(--admin-text-dark);
    margin-left: 0.5rem;
}

/* Button Enhancements */
.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, rgba(var(--admin-primary-rgb), 0.8) 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.875rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(var(--admin-primary-rgb), 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.875rem 2rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 0.25rem 0.75rem rgba(108, 117, 125, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(108, 117, 125, 0.4);
}

/* Section Headers */
.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.04);
}

.section-header h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--admin-text-dark), rgba(44, 62, 80, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--admin-text-muted);
    margin-bottom: 0;
}

/* Input Groups */
.input-group-enhanced {
    margin-bottom: 1.5rem;
}

.input-group-enhanced .form-label {
    margin-bottom: 0.75rem;
}

/* Color Picker Enhancement */
.color-picker-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.color-picker-group .form-control[type="color"] {
    width: 60px;
    height: 45px;
    padding: 0.25rem;
    border-radius: 0.5rem;
    cursor: pointer;
}

.color-picker-group .form-control[type="text"] {
    flex: 1;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Form Actions */
.form-actions {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    margin-top: 2rem;
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

.form-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.6), var(--admin-primary));
}

.form-actions .btn {
    margin: 0 0.5rem;
    min-width: 150px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.form-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.form-actions .btn:hover::before {
    left: 100%;
}

.form-actions .btn:hover {
    transform: translateY(-2px);
}

.form-actions .btn-primary {
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border: none;
    box-shadow: 0 0.5rem 1.5rem rgba(var(--admin-primary-rgb), 0.3);
}

.form-actions .btn-primary:hover {
    box-shadow: 0 0.75rem 2rem rgba(var(--admin-primary-rgb), 0.4);
}

.form-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border: none;
    box-shadow: 0 0.5rem 1.5rem rgba(108, 117, 125, 0.3);
}

.form-actions .btn-secondary:hover {
    box-shadow: 0 0.75rem 2rem rgba(108, 117, 125, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .settings-card .card-body {
        padding: 1rem;
    }
    
    .form-actions {
        padding: 1.5rem;
    }
    
    .form-actions .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
    
    .color-picker-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .color-picker-group .form-control[type="color"] {
        width: 100%;
        height: 50px;
    }
}

/* Loading States */
.form-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success States */
.form-success {
    border-color: #28a745 !important;
    background: rgba(40, 167, 69, 0.05) !important;
}

.success-icon {
    color: #28a745;
    font-size: 1.25rem;
    margin-left: 0.5rem;
}

/* Validation States */
.is-invalid {
    border-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.05) !important;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

/* Tooltips */
.tooltip-trigger {
    cursor: help;
    color: var(--admin-text-muted);
    margin-left: 0.25rem;
}

.tooltip-trigger:hover {
    color: var(--admin-primary);
}

/* Enhanced Form Switch Styling */
.form-check.form-switch .form-check-input {
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 1rem;
    background-color: #e9ecef;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-left: 0;
}

.form-check.form-switch .form-check-input:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--admin-primary-rgb), 0.25);
}

.form-check.form-switch .form-check-input:checked {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
    box-shadow: 0 0.125rem 0.5rem rgba(var(--admin-primary-rgb), 0.3);
}

.form-check.form-switch .form-check-input:hover {
    border-color: var(--admin-primary);
}

.form-check.form-switch .form-check-label {
    font-weight: 500;
    color: var(--admin-text-dark);
    margin-left: 0.75rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.form-check.form-switch .form-check-label:hover {
    color: var(--admin-primary);
}

.form-check.form-switch {
    padding-left: 0;
    margin-bottom: 0.75rem;
}
