<?php
/**
 * Bamboo Web Application - System Settings
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $settings_to_update = [
            'app_name',
            'app_description',
            'company_name',
            'contact_email',
            'contact_phone',
            'address',
            'primary_color',
            'secondary_color',
            'default_currency',
            'decimal_places',
            'timezone',
            'maintenance_mode',
            'registration_enabled',
            'email_verification_required',
            'opening_hours',
            'closing_hours',
            'signup_bonus',
            'min_wallet_balance',
            'contract_terms',
            'about_us',
            'faq_content',
            'latest_events',
            'user_registration_agreement'
        ];
        
        $updated = 0;
        
        // Handle file uploads
        if (isset($_FILES['app_logo']) && $_FILES['app_logo']['error'] === UPLOAD_ERR_OK) {
            $upload_result = handleFileUpload($_FILES['app_logo'], 'uploads/logos/', ['jpg', 'jpeg', 'png', 'gif', 'svg']);
            if ($upload_result['success']) {
                updateSetting('app_logo', $upload_result['filename']);
                $updated++;
                showSuccess('Logo uploaded successfully!');
            } else {
                showError('Logo upload failed: ' . $upload_result['message']);
            }
        }
        
        if (isset($_FILES['app_certificate']) && $_FILES['app_certificate']['error'] === UPLOAD_ERR_OK) {
            $upload_result = handleFileUpload($_FILES['app_certificate'], 'uploads/certificates/', ['jpg', 'jpeg', 'png', 'pdf']);
            if ($upload_result['success']) {
                updateSetting('app_certificate', $upload_result['filename']);
                $updated++;
            } else {
                showError('Certificate upload failed: ' . $upload_result['message']);
            }
        }
        
        foreach ($settings_to_update as $setting) {
            if (isset($_POST[$setting])) {
                $value = $_POST[$setting];
                if (in_array($setting, ['maintenance_mode', 'registration_enabled', 'email_verification_required'])) {
                    $value = isset($_POST[$setting]) ? '1' : '0';
                }
                if (updateSetting($setting, $value)) {
                    $updated++;
                }
            }
        }
        
        if ($updated > 0) {
            showSuccess('Settings updated successfully!');
        } else {
            showError('No settings were updated.');
        }
    }
}

// Get current settings
$current_settings = getAppSettings();

// Page configuration
$page_title = 'System Settings';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css'
];

// Include admin header
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <!-- Sidebar -->
    <?php include '../includes/admin_sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <?php include '../includes/admin_topbar.php'; ?>
        
        <!-- Content Area -->
        <div class="admin-content">
            <div class="container-fluid">
                
                <!-- Enhanced Page Header -->
                <div class="settings-page-header mb-5">
                    <div class="header-content">
                        <div class="header-icon">
                            <i class="bi bi-sliders"></i>
                        </div>
                        <div class="header-text">
                            <h1 class="page-title">System Settings</h1>
                            <p class="page-subtitle">Configure your application settings and preferences</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-2"></i>Reset Form
                        </button>
                    </div>
                </div>
                
                <form method="POST" action="" enctype="multipart/form-data" id="settingsForm" class="settings-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- App Configuration -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card app-config-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-gear"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">App Configuration</h5>
                                        <p class="card-subtitle">Basic application settings and branding</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="app_name" class="form-label">
                                            <i class="bi bi-app-indicator me-2"></i>App Name
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-tag"></i>
                                            </span>
                                            <input type="text" class="form-control" id="app_name" name="app_name"
                                                   value="<?php echo htmlspecialchars($current_settings['app_name'] ?? 'Bamboo'); ?>"
                                                   required placeholder="Enter application name">
                                        </div>
                                        <div class="form-text">This name will appear throughout the application</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="app_logo" class="form-label">
                                            <i class="bi bi-image me-2"></i>App Logo
                                        </label>
                                        <div class="logo-upload-container">
                                            <div class="logo-preview-wrapper">
                                                <div class="logo-preview" id="logoPreview">
                                                    <?php if (!empty($current_settings['app_logo'])): ?>
                                                        <?php
                                                        $logo_path = BASE_URL . 'uploads/logos/' . $current_settings['app_logo'];
                                                        $file_extension = strtolower(pathinfo($current_settings['app_logo'], PATHINFO_EXTENSION));
                                                        ?>
                                                        <?php if ($file_extension === 'svg'): ?>
                                                            <div class="svg-preview">
                                                                <object data="<?php echo $logo_path; ?>" type="image/svg+xml" class="preview-svg">
                                                                    <img src="<?php echo $logo_path; ?>" alt="Current Logo" class="preview-image">
                                                                </object>
                                                            </div>
                                                        <?php else: ?>
                                                            <img src="<?php echo $logo_path; ?>" alt="Current Logo" class="preview-image">
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <div class="preview-placeholder">
                                                            <i class="bi bi-cloud-upload"></i>
                                                            <span>Click to upload logo</span>
                                                            <small>SVG, PNG, JPG, GIF supported</small>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="upload-overlay">
                                                    <i class="bi bi-camera"></i>
                                                    <span>Change Logo</span>
                                                </div>
                                            </div>
                                            <input type="file" class="form-control logo-input" id="app_logo" name="app_logo"
                                                   accept="image/*,.svg" onchange="previewLogo(this)">
                                            <div class="form-text">
                                                <i class="bi bi-info-circle me-1"></i>
                                                Upload a logo for your application (SVG, PNG, JPG, GIF - Max 2MB)
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group full-width">
                                        <label for="app_certificate" class="form-label">
                                            <i class="bi bi-file-earmark-lock me-2"></i>App Certificate
                                        </label>
                                        <input type="file" class="form-control" id="app_certificate" name="app_certificate" accept="image/*,.pdf">
                                        <?php if (!empty($current_settings['app_certificate'])): ?>
                                            <div class="form-text">Current: <?php echo htmlspecialchars($current_settings['app_certificate']); ?></div>
                                        <?php endif; ?>
                                        <div class="form-text">Upload app certificate (Image or PDF format)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="opening_hours" class="form-label">
                                            <i class="bi bi-clock me-2"></i>Opening Hours
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-sunrise"></i>
                                            </span>
                                            <input type="number" class="form-control" id="opening_hours" name="opening_hours"
                                                   value="<?php echo htmlspecialchars($current_settings['opening_hours'] ?? '9'); ?>"
                                                   min="0" max="23" placeholder="9">
                                            <span class="input-group-text">:00</span>
                                        </div>
                                        <div class="form-text">Business opening hour (24-hour format)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="closing_hours" class="form-label">
                                            <i class="bi bi-clock me-2"></i>Closing Hours
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-sunset"></i>
                                            </span>
                                            <input type="number" class="form-control" id="closing_hours" name="closing_hours"
                                                   value="<?php echo htmlspecialchars($current_settings['closing_hours'] ?? '21'); ?>"
                                                   min="0" max="23" placeholder="21">
                                            <span class="input-group-text">:00</span>
                                        </div>
                                        <div class="form-text">Business closing hour (24-hour format)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="signup_bonus" class="form-label">
                                            <i class="bi bi-gift me-2"></i>Sign Up Bonus
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo getAppSetting('default_currency', DEFAULT_CURRENCY); ?></span>
                                            <input type="number" class="form-control" id="signup_bonus" name="signup_bonus"
                                                   value="<?php echo htmlspecialchars($current_settings['signup_bonus'] ?? '0.00'); ?>"
                                                   step="0.01" min="0" placeholder="0.00">
                                        </div>
                                        <div class="form-text">Bonus amount given to new users upon registration</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="min_wallet_balance" class="form-label">
                                            <i class="bi bi-wallet2 me-2"></i>Minimum Wallet Balance
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo getAppSetting('default_currency', DEFAULT_CURRENCY); ?></span>
                                            <input type="number" class="form-control" id="min_wallet_balance" name="min_wallet_balance"
                                                   value="<?php echo htmlspecialchars($current_settings['min_wallet_balance'] ?? '45'); ?>"
                                                   step="0.01" min="0" placeholder="45.00">
                                        </div>
                                        <div class="form-text">Minimum balance required for receiving orders</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-envelope"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">Contact Information</h5>
                                        <p class="card-subtitle">Company contact details and support information</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="contact_email" class="form-label">
                                            <i class="bi bi-envelope me-2"></i>Contact Email
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-at"></i>
                                            </span>
                                            <input type="email" class="form-control" id="contact_email" name="contact_email"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_email'] ?? ''); ?>"
                                                   placeholder="Enter contact email">
                                        </div>
                                        <div class="form-text">Primary email for customer support and inquiries</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="contact_phone" class="form-label">
                                            <i class="bi bi-telephone me-2"></i>Contact Phone
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-phone"></i>
                                            </span>
                                            <input type="text" class="form-control" id="contact_phone" name="contact_phone"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_phone'] ?? ''); ?>"
                                                   placeholder="Enter contact phone">
                                        </div>
                                        <div class="form-text">Primary phone number for customer support</div>
                                    </div>

                                    <div class="form-group full-width">
                                        <label for="address" class="form-label">
                                            <i class="bi bi-geo-alt me-2"></i>Address
                                        </label>
                                        <textarea class="form-control" id="address" name="address" rows="3"
                                                  placeholder="Enter company address"><?php echo htmlspecialchars($current_settings['address'] ?? ''); ?></textarea>
                                        <div class="form-text">Physical address for company location</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Settings -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">Financial Settings</h5>
                                        <p class="card-subtitle">Currency and financial display preferences</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="default_currency" class="form-label">
                                            <i class="bi bi-currency-exchange me-2"></i>Default Currency
                                        </label>
                                        <select class="form-select" id="default_currency" name="default_currency">
                                            <?php
                                            $currencies = ['$', '€', '£', '¥', '₹', '₦'];
                                            $current_currency = $current_settings['default_currency'] ?? DEFAULT_CURRENCY;
                                            foreach ($currencies as $currency) {
                                                $selected = $currency === $current_currency ? 'selected' : '';
                                                echo "<option value=\"{$currency}\" {$selected}>{$currency}</option>";
                                            }
                                            ?>
                                        </select>
                                        <div class="form-text">Currency symbol used throughout the application</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="decimal_places" class="form-label">
                                            <i class="bi bi-123 me-2"></i>Decimal Places
                                        </label>
                                        <select class="form-select" id="decimal_places" name="decimal_places">
                                            <?php
                                            $current_decimals = $current_settings['decimal_places'] ?? DECIMAL_PLACES;
                                            for ($i = 0; $i <= 4; $i++) {
                                                $selected = $i == $current_decimals ? 'selected' : '';
                                                echo "<option value=\"{$i}\" {$selected}>{$i}</option>";
                                            }
                                            ?>
                                        </select>
                                        <div class="form-text">Number of decimal places for currency display</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contract Terms -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-file-text"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">Contract Terms</h5>
                                        <p class="card-subtitle">Terms and conditions for user agreements</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group full-width">
                                        <label for="contract_terms" class="form-label">
                                            <i class="bi bi-file-earmark-text me-2"></i>Contract Rules
                                        </label>
                                        <textarea class="form-control" id="contract_terms" name="contract_terms" rows="15"
                                                  placeholder="Enter contract terms and conditions..."><?php echo htmlspecialchars($current_settings['contract_terms'] ?? '1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of 100 USDT.

1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.

2) User withdrawals and system withdrawal requirements / security of user funds

2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements

2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.

2.3) All users are not allowed to apply for withdrawal in the middle of task to avoid affecting the merchant\'s operation

2.4) Users\' funds are completely safe on the Platform and the Platform will be liable for any accidental loss

(3) Please do not disclose your account password and withdrawal password to others. The platform will not be held responsible for any loss or damage caused.

3.1) All users are advised to keep their accounts secure to avoid disclosure 

(3.2) The Platform is not responsible for any accidental disclosure of accounts

3.3) Because of the financial implications of the accounts, it is important not to disclose them to avoid unnecessary problems.

3.4) Withdrawal password It is recommended that you do not set a birthday password, ID card number or mobile phone number, etc. It is recommended that you set a more difficult password to protect your funds.

3.5) If you forget your password, you can reset it by contacting the online service and be sure to change it yourself afterwards.

(4) Optimization rating are randomly assigned by the system and therefore cannot be changed, canceled, controlled or skipped in any way

4.1) Due to a large number of users on the platform, it is not possible to distribute group purchase items manually, so all group purchase items are distributed randomly by the system.

(4.2) Group purchase/combination items are randomly released by the system and cannot be changed/cancelled/skipped by any user/staff.

5. Legal action will be taken in the event of misuse of the account

6. Each item comes from a different merchant, no deposit for more than 10 minutes, and each deposit must be made with the online service to confirm the merchant\'s cryptocurrency wallet address

7. The platform will not be held responsible for any deposits made to the wrong wallet address

8. Each time optimization rating, the user is required to complete it within 2 hours, if it is not completed without notifying the merchant to apply for a time extension, resulting in complaints from the merchant, the user is liable for breach of contract'); ?></textarea>
                                        <div class="form-text">Terms and conditions that users must agree to</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- About Us -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-info-circle"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">About Us</h5>
                                        <p class="card-subtitle">Company information and description</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group full-width">
                                        <label for="about_us" class="form-label">
                                            <i class="bi bi-building me-2"></i>About Us Content
                                        </label>
                                        <textarea class="form-control" id="about_us" name="about_us" rows="8"
                                                  placeholder="Enter company description and information..."><?php echo htmlspecialchars($current_settings['about_us'] ?? 'Bamboo has been a trusted partner in the truest sense of the word.

We approach growth from multiple angles.

Most agencies only focus on channel management to determine success. This is no longer enough. Bamboo looks beyond the channels to understand, forecast, and make smarter investments over time'); ?></textarea>
                                        <div class="form-text">Information about your company and services</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Content -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-question-circle"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">FAQ Content</h5>
                                        <p class="card-subtitle">Frequently asked questions and answers</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group full-width">
                                        <label for="faq_content" class="form-label">
                                            <i class="bi bi-patch-question me-2"></i>Frequently Asked Questions
                                        </label>
                                        <textarea class="form-control" id="faq_content" name="faq_content" rows="8"
                                                  placeholder="Enter frequently asked questions and answers..."><?php echo htmlspecialchars($current_settings['faq_content'] ?? 'I. Start Product Optimization Task
1.1) Minimum account balance of 100 USDT for the first 40 tasks/set
1.2) A minimum renewal of 100 USDT is required to start the tasks
1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.

II. Withdrawal
2.1) Full withdrawal amount can be requested after completing 1 group of task
2.2) Users need to complete 1 group of tasks before they can request a withdrawal.'); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Latest Events -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-calendar-event"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">Latest Events</h5>
                                        <p class="card-subtitle">Current events and announcements</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group full-width">
                                        <label for="latest_events" class="form-label">
                                            <i class="bi bi-megaphone me-2"></i>Latest Events Content
                                        </label>
                                        <textarea class="form-control" id="latest_events" name="latest_events" rows="8"
                                                  placeholder="Enter latest events and announcements..."><?php echo htmlspecialchars($current_settings['latest_events'] ?? 'I. Start Product Optimization Task
1.1) Minimum account balance of 100 USDT for the first 40 tasks/set
1.2) A minimum renewal of 100USDT is required to start the tasks
1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.'); ?></textarea>
                                        <div class="form-text">Latest events and announcements for users</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Registration Agreement -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">User Registration Agreement</h5>
                                        <p class="card-subtitle">Terms users must agree to during registration</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group full-width">
                                        <label for="user_registration_agreement" class="form-label">
                                            <i class="bi bi-file-earmark-check me-2"></i>Registration Agreement Content
                                        </label>
                                        <textarea class="form-control" id="user_registration_agreement" name="user_registration_agreement" rows="8"
                                                  placeholder="Enter user registration agreement terms..."><?php echo htmlspecialchars($current_settings['user_registration_agreement'] ?? 'Contract Rules

1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of 100 USDT.

1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.

2) User withdrawals and system withdrawal requirements / security of user funds

2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements

2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.'); ?></textarea>
                                        <div class="form-text">Agreement terms users must accept during registration</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Options -->
                    <div class="settings-section mb-4">
                        <div class="card settings-card">
                            <div class="card-header">
                                <div class="card-header-content">
                                    <div class="card-icon">
                                        <i class="bi bi-toggles"></i>
                                    </div>
                                    <div class="card-title-group">
                                        <h5 class="card-title mb-0">System Options</h5>
                                        <p class="card-subtitle">System-wide settings and toggles</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="bi bi-tools me-2"></i>Maintenance Mode
                                        </label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode"
                                                   <?php echo ($current_settings['maintenance_mode'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="maintenance_mode">
                                                Enable Maintenance Mode
                                            </label>
                                        </div>
                                        <div class="form-text">Temporarily disable the site for maintenance</div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="bi bi-person-plus me-2"></i>User Registration
                                        </label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="registration_enabled" name="registration_enabled"
                                                   <?php echo ($current_settings['registration_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="registration_enabled">
                                                Allow New Registrations
                                            </label>
                                        </div>
                                        <div class="form-text">Allow new users to register accounts</div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="bi bi-envelope-check me-2"></i>Email Verification
                                        </label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="email_verification_required" name="email_verification_required"
                                                   <?php echo ($current_settings['email_verification_required'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="email_verification_required">
                                                Require Email Verification
                                            </label>
                                        </div>
                                        <div class="form-text">Require email verification for new accounts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-check-circle me-2"></i>Save Configuration
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-2"></i>Reset Form
                        </button>
                    </div>
                </form>
                
            </div>
        </div>
        
        <!-- Footer -->
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js',
    BASE_URL . 'admin/settings/settings.js'
];
include '../includes/admin_footer_scripts.php';
?>