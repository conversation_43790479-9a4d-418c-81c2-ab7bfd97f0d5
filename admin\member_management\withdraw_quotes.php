<?php
/**
 * Bamboo Web Application - Withdrawal Quotes Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, balance FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

$page_title = 'Withdrawal Quotes for ' . htmlspecialchars($user['username']);
include '../includes/admin_header.php';

// Handle form submission for adding withdrawal quote
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $message = sanitizeInput($_POST['message']);
        if (!empty($message)) {
            insertRecord('withdrawal_quotes', [
                'user_id' => $user_id,
                'message' => $message,
                'admin_id_created' => $_SESSION['admin_id']
            ]);
            showSuccess('Withdrawal quote added successfully!');
        } else {
            showError('Message cannot be empty.');
        }
        redirect('admin/member_management/withdraw_quotes.php?id=' . $user_id);
        exit();
    }
}

// Handle resolution or deletion of withdrawal quote
if (isset($_GET['action']) && isset($_GET['quote_id'])) {
    $quote_id = (int)($_GET['quote_id'] ?? 0);
    if ($quote_id > 0) {
        if ($_GET['action'] === 'resolve') {
            updateRecord('withdrawal_quotes', ['status' => 'resolved'], 'id = ? AND user_id = ?', [$quote_id, $user_id]);
            showSuccess('Withdrawal quote resolved successfully!');
        } elseif ($_GET['action'] === 'delete') {
            deleteRecord('withdrawal_quotes', 'id = ? AND user_id = ?', [$quote_id, $user_id]);
            showSuccess('Withdrawal quote deleted successfully!');
        }
    } else {
        showError('Invalid quote ID.');
    }
    redirect('admin/member_management/withdraw_quotes.php?id=' . $user_id);
    exit();
}

$quotes = fetchAll('SELECT * FROM withdrawal_quotes WHERE user_id = ? ORDER BY created_at DESC', [$user_id]);

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Withdrawal Quotes for: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">User Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                        <p><strong>Wallet Balance:</strong> <?php echo formatCurrency($user['balance']); ?></p>
                        <p><strong>Order Progress:</strong> (Completed / Total Quantity) - *This data needs to be fetched dynamically*</p>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Add New Withdrawal Quote</h5>
                    </div>
                    <div class="card-body">
                        <form action="withdraw_quotes.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-3">
                                <label for="message" class="form-label">Message</label>
                                <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Add Quote</button>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Existing Withdrawal Quotes</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($quotes)): ?>
                            <p class="text-muted">No withdrawal quotes found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>S/N</th>
                                            <th>Message</th>
                                            <th>Status</th>
                                            <th>Creation Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $sn = 1; foreach ($quotes as $quote): ?>
                                            <tr>
                                                <td><?php echo $sn++; ?></td>
                                                <td><?php echo htmlspecialchars($quote['message']); ?></td>
                                                <td><?php echo $quote['status'] === 'active' ? '<span class="badge bg-warning">Active</span>' : '<span class="badge bg-success">Resolved</span>'; ?></td>
                                                <td><?php echo formatDate($quote['created_at']); ?></td>
                                                <td>
                                                    <?php if ($quote['status'] === 'active'): ?>
                                                        <a href="withdraw_quotes.php?id=<?php echo $user_id; ?>&action=resolve&quote_id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-success">Resolve</a>
                                                    <?php endif; ?>
                                                    <a href="withdraw_quotes.php?id=<?php echo $user_id; ?>&action=delete&quote_id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this quote?')">Delete</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
