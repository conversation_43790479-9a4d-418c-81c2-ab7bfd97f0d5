/* Member Details Styling */
.info-group {
    margin-bottom: 1rem;
}

.info-label {
    font-weight: bold;
    color: #555;
}

.info-value {
    color: #333;
}

.info-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.info-row:last-child {
    border-bottom: none;
}

/* Quick Actions Styling */
.quick-actions-card {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 1rem;
    overflow: hidden;
}

.quick-actions-card .card-header {
    border-bottom: none;
    padding: 1.5rem;
}

.quick-action-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-item:hover {
    background: rgba(0, 123, 255, 0.05);
    color: inherit;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}

.quick-action-item.danger:hover {
    background: rgba(220, 53, 69, 0.05);
}

.quick-action-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.2);
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    color: #2c3e50;
}

.quick-action-desc {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0;
}

/* Remove borders from last items in each row */
.quick-action-item:nth-child(4n) {
    border-right: none;
}

/* Financial Items Styling */
.financial-item {
    padding: 1rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #28a745;
}

.financial-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.financial-value {
    font-size: 1.25rem;
    margin: 0;
}

/* Enhanced Card Headers */
.card-header h5 {
    display: flex;
    align-items: center;
}

.card-header h5 i {
    color: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .quick-action-item {
        border-right: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .quick-action-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .quick-action-title {
        font-size: 0.875rem;
    }

    .quick-action-desc {
        font-size: 0.75rem;
    }

    .financial-value {
        font-size: 1.1rem;
    }
}
