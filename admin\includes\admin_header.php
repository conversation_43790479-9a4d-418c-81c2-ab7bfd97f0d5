<?php
/**
 * Bamboo Web Application - Admin Header
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get app settings
$app_name = getAppSetting('app_name', APP_NAME);
$app_logo = getAppSetting('app_logo', '');
$primary_color = getAppSetting('primary_color', '#ff6900');
$secondary_color = getAppSetting('secondary_color', '#ffffff');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo htmlspecialchars($page_title ?? $app_name . ' - Admin'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>images/favicon.ico">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Admin CSS -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/admin.css" rel="stylesheet">
    
    <!-- White Theme Override - Ensures all admin pages have white background -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/admin-white-theme.css" rel="stylesheet">
    
    <!-- Additional CSS files -->
    <?php if (isset($additional_css) && is_array($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link href="<?php echo $css_file; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Dynamic Theme Colors -->
    <style>
        :root {
            --admin-primary: <?php echo $primary_color; ?>;
            --admin-secondary: <?php echo $secondary_color; ?>;
            --admin-primary-rgb: <?php echo implode(',', sscanf($primary_color, "#%02x%02x%02x")); ?>;
        }
    </style>
    
    <!-- Meta tags -->
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="<?php echo $primary_color; ?>">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
</head>
<body class="<?php echo $body_class ?? 'admin-page'; ?>">
    
    <!-- Loading Spinner -->
    <div id="admin-loading" class="admin-loading">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    
    <!-- Flash Messages -->
    <?php $flash_messages = getFlashMessages(); ?>
    <?php if (!empty($flash_messages)): ?>
        <div class="admin-flash-messages">
            <?php if (isset($flash_messages['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($flash_messages['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>