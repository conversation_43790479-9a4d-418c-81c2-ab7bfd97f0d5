<?php
/**
 * Bamboo Web Application - Member Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// Search and filter parameters
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$vip_level = $_GET['vip_level'] ?? '';

// Pagination parameters
$page = (int)($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build the query
$query = "SELECT u.*, v.name as vip_name FROM users u LEFT JOIN vip_levels v ON u.vip_level = v.level WHERE 1=1";
$params = [];

if (!empty($search)) {
    $query .= " AND (u.username LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)";
    $search_param = "%{$search}%";
    array_push($params, $search_param, $search_param, $search_param);
}

if (!empty($status)) {
    $query .= " AND u.status = ?";
    $params[] = $status;
}

if (!empty($vip_level)) {
    $query .= " AND u.vip_level = ?";
    $params[] = $vip_level;
}

// Get total records for pagination
$total_query = str_replace("SELECT u.*, v.name as vip_name", "SELECT COUNT(u.id)", $query);
$total_records = fetchRow($total_query, $params)['COUNT(u.id)'];
$total_pages = ceil($total_records / $per_page);

// Add limit for pagination
$query .= " ORDER BY u.created_at DESC LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;

$users = fetchAll($query, $params);
$vip_levels = fetchAll("SELECT * FROM vip_levels ORDER BY level");

$page_title = 'Member Management';
$additional_css = [
    BASE_URL . 'admin/member_management/member-management.css'
];
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Member Management</h1>
                    <a href="add.php" class="btn btn-primary"><i class="bi bi-plus-circle me-2"></i>Add New Member</a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">All Users</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3 mb-4">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="Search by username, email, phone..." value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    <option value="active" <?php if ($status === 'active') echo 'selected'; ?>>Active</option>
                                    <option value="pending" <?php if ($status === 'pending') echo 'selected'; ?>>Pending</option>
                                    <option value="suspended" <?php if ($status === 'suspended') echo 'selected'; ?>>Suspended</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="vip_level" class="form-select">
                                    <option value="">All VIP Levels</option>
                                    <?php foreach ($vip_levels as $level): ?>
                                        <option value="<?php echo $level['level']; ?>" <?php if ($vip_level == $level['level']) echo 'selected'; ?>><?php echo htmlspecialchars($level['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">Filter</button>
                            </div>
                        </form>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Member</th>
                                        <th>Contact</th>
                                        <th>Balance</th>
                                        <th>VIP Level</th>
                                        <th>Tasks</th>
                                        <th>Status</th>
                                        <th>Joined</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($users)): ?>
                                        <tr>
                                            <td colspan="9" class="text-center">No users found.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($users as $user): ?>
                                            <?php
                                            // Get task statistics for this user
                                            $today_start = date('Y-m-d 00:00:00');
                                            $today_end = date('Y-m-d 23:59:59');
                                            $tasks_today = fetchOne('SELECT COUNT(*) FROM tasks WHERE user_id = ? AND assigned_at BETWEEN ? AND ?', [$user['id'], $today_start, $today_end]) ?? 0;
                                            $completed_today = fetchOne('SELECT COUNT(*) FROM tasks WHERE user_id = ? AND status = "completed" AND completed_at BETWEEN ? AND ?', [$user['id'], $today_start, $today_end]) ?? 0;
                                            $total_tasks = fetchOne('SELECT COUNT(*) FROM tasks WHERE user_id = ?', [$user['id']]) ?? 0;
                                            $total_completed = fetchOne('SELECT COUNT(*) FROM tasks WHERE user_id = ? AND status = "completed"', [$user['id']]) ?? 0;
                                            ?>
                                            <tr>
                                                <td><strong><?php echo $user['id']; ?></strong></td>
                                                <td>
                                                    <div class="member-info-with-avatar">
                                                        <div class="member-avatar">
                                                            <?php if (!empty($user['avatar'])): ?>
                                                                <img src="<?php echo BASE_URL . 'uploads/avatars/' . $user['avatar']; ?>" alt="Avatar" class="avatar-sm">
                                                            <?php else: ?>
                                                                <div class="avatar-initials-sm">
                                                                    <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="member-details">
                                                            <div class="username"><?php echo htmlspecialchars(ucfirst($user['username'])); ?></div>
                                                            <div class="member-id">ID: <?php echo $user['id']; ?></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="contact-info">
                                                        <div class="email"><?php echo htmlspecialchars($user['email']); ?></div>
                                                        <div class="phone"><?php echo htmlspecialchars($user['phone']); ?></div>
                                                    </div>
                                                </td>
                                                <td><?php echo formatCurrency($user['balance']); ?></td>
                                                <td><span class="badge bg-info"><?php echo htmlspecialchars($user['vip_name'] ?? 'N/A'); ?></span></td>
                                                <td>
                                                    <div class="task-stats">
                                                        <div class="task-today">
                                                            <small class="text-muted">Today:</small>
                                                            <span class="badge bg-primary"><?php echo $completed_today; ?>/<?php echo $tasks_today; ?></span>
                                                        </div>
                                                        <div class="task-total">
                                                            <small class="text-muted">Total:</small>
                                                            <span class="badge bg-secondary"><?php echo $total_completed; ?>/<?php echo $total_tasks; ?></span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-<?php echo getStatusBadgeClass($user['status']); ?>"><?php echo ucfirst($user['status']); ?></span></td>
                                                <td><?php echo formatDate($user['created_at']); ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="view.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary" title="View">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="edit.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-secondary" title="Edit">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <a href="delete.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this user?')">
                                                            <i class="bi bi-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item"><a class="page-link" href="?page=<?php echo (int)$page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&vip_level=<?php echo $vip_level; ?>">Previous</a></li>
                                <?php endif; ?>
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?php if ($i === $page) echo 'active'; ?>"><a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&vip_level=<?php echo $vip_level; ?>"><?php echo $i; ?></a></li>
                                <?php endfor; ?>
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item"><a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&vip_level=<?php echo $vip_level; ?>">Next</a></li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
