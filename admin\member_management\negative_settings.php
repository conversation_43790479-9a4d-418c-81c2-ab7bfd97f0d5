<?php
/**
 * Bamboo Web Application - Negative Settings Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, balance FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

$page_title = 'Negative Settings for ' . htmlspecialchars($user['username']);
include '../includes/admin_header.php';

// Handle form submission for adding negative setting
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $trigger_task_number = (int)$_POST['trigger_task_number'];
        $product_id_override = (int)$_POST['product_id_override'];
        $override_amount = (float)$_POST['override_amount'];

        if ($trigger_task_number > 0 && $product_id_override > 0 && $override_amount > 0) {
            insertRecord('negative_settings', [
                'user_id' => $user_id,
                'trigger_task_number' => $trigger_task_number,
                'product_id_override' => $product_id_override,
                'override_amount' => $override_amount,
                'admin_id_created' => $_SESSION['admin_id']
            ]);
            showSuccess('Negative setting added successfully!');
        } else {
            showError('Invalid data provided.');
        }
        redirect('admin/member_management/negative_settings.php?id=' . $user_id);
        exit();
    }
}

// Handle deletion of negative setting
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['setting_id'])) {
    $setting_id = (int)($_GET['setting_id'] ?? 0);
    if ($setting_id > 0) {
        deleteRecord('negative_settings', 'id = ? AND user_id = ?', [$setting_id, $user_id]);
        showSuccess('Negative setting deleted successfully!');
    } else {
        showError('Invalid setting ID.');
    }
    redirect('admin/member_management/negative_settings.php?id=' . $user_id);
    exit();
}

$products = fetchAll('SELECT id, name FROM products');
$negative_settings = fetchAll('SELECT ns.*, p.name as product_name FROM negative_settings ns JOIN products p ON ns.product_id_override = p.id WHERE ns.user_id = ?', [$user_id]);

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Negative Settings for: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">User Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                        <p><strong>Wallet Balance:</strong> <?php echo formatCurrency($user['balance']); ?></p>
                        <p><strong>Order Progress:</strong> (Completed / Total Quantity) - *This data needs to be fetched dynamically*</p>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Add Negative Setting</h5>
                    </div>
                    <div class="card-body">
                        <form action="negative_settings.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="trigger_task_number" class="form-label">Trigger Task Number</label>
                                    <input type="number" class="form-control" id="trigger_task_number" name="trigger_task_number" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="product_id_override" class="form-label">Product</label>
                                    <select class="form-select" id="product_id_override" name="product_id_override" required>
                                        <?php foreach ($products as $product): ?>
                                            <option value="<?php echo $product['id']; ?>"><?php echo htmlspecialchars($product['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="override_amount" class="form-label">Override Amount</label>
                                    <input type="number" class="form-control" id="override_amount" name="override_amount" step="0.01" required>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Add Setting</button>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Existing Negative Settings</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($negative_settings)): ?>
                            <p class="text-muted">No negative settings found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>S/N</th>
                                            <th>Trigger Task</th>
                                            <th>Product</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Creation Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $sn = 1; foreach ($negative_settings as $setting): ?>
                                            <tr>
                                                <td><?php echo $sn++; ?></td>
                                                <td><?php echo $setting['trigger_task_number']; ?></td>
                                                <td><?php echo htmlspecialchars($setting['product_name']); ?></td>
                                                <td><?php echo formatCurrency($setting['override_amount']); ?></td>
                                                <td><?php echo $setting['is_triggered'] ? '<span class="badge bg-success">Triggered</span>' : '<span class="badge bg-warning">Pending</span>'; ?></td>
                                                <td><?php echo formatDate($setting['created_at']); ?></td>
                                                <td>
                                                    <a href="negative_settings.php?id=<?php echo $user_id; ?>&action=delete&setting_id=<?php echo $setting['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this setting?')">Delete</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
