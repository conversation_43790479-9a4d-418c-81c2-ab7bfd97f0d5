/**
 * Bamboo Web Application - System Settings JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize settings functionality
    initializeSettings();
});

function initializeSettings() {
    // Setup form validation
    setupFormValidation();
    
    // Setup file upload previews
    setupFileUploadPreviews();
    
    // Setup color picker synchronization
    setupColorPickers();
}

function setupFormValidation() {
    const form = document.getElementById('settingsForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
            
            // Add loading state
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Saving...';
            }
        });
    }
}

function validateForm() {
    let isValid = true;
    const requiredFields = document.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Validate email format
    const emailFields = document.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        }
    });
    
    // Validate URL format
    const urlFields = document.querySelectorAll('input[type="url"]');
    urlFields.forEach(field => {
        if (field.value && !isValidUrl(field.value)) {
            showFieldError(field, 'Please enter a valid URL');
            isValid = false;
        }
    });
    
    return isValid;
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

function setupFileUploadPreviews() {
    // Logo preview
    const logoInput = document.getElementById('app_logo');
    const logoPreviewWrapper = document.querySelector('.logo-preview-wrapper');

    if (logoInput) {
        logoInput.addEventListener('change', function() {
            previewLogo(this);
        });
    }

    // Make logo preview wrapper clickable
    if (logoPreviewWrapper && logoInput) {
        logoPreviewWrapper.addEventListener('click', function() {
            logoInput.click();
        });
    }
    
    // Certificate preview (if exists)
    const certInput = document.getElementById('ssl_certificate');
    if (certInput) {
        certInput.addEventListener('change', function() {
            previewCertificate(this);
        });
    }
}

function previewLogo(input) {
    const preview = document.getElementById('logoPreview');
    if (!preview) return;
    
    if (input.files && input.files[0]) {
        const file = input.files[0];
        
        // Validate file type (including SVG)
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];
        if (!allowedTypes.includes(file.type)) {
            showNotification('Please select a valid image file (JPG, PNG, GIF, SVG)', 'error');
            input.value = '';
            return;
        }
        
        // Validate file size (2MB max)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('File size must be less than 2MB', 'error');
            input.value = '';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const fileExtension = file.name.split('.').pop().toLowerCase();

            if (fileExtension === 'svg') {
                // Handle SVG files
                preview.innerHTML = `
                    <div class="svg-preview">
                        <object data="${e.target.result}" type="image/svg+xml" class="preview-svg">
                            <img src="${e.target.result}" alt="Logo Preview" class="preview-image">
                        </object>
                    </div>
                `;
            } else {
                // Handle regular image files
                preview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="preview-image">`;
            }

            // Add success notification
            showNotification('Logo preview updated successfully', 'success');
        };

        reader.onerror = function() {
            showNotification('Error reading file. Please try again.', 'error');
            input.value = '';
        };

        reader.readAsDataURL(file);
    } else {
        // Reset to placeholder if no file selected
        preview.innerHTML = `
            <div class="preview-placeholder">
                <i class="bi bi-cloud-upload"></i>
                <span>Click to upload logo</span>
                <small>SVG, PNG, JPG, GIF supported</small>
            </div>
        `;
    }
}

function previewCertificate(input) {
    const preview = document.getElementById('certificatePreview');
    if (!preview) return;
    
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const fileName = file.name;
        const fileSize = (file.size / 1024).toFixed(2) + ' KB';
        
        preview.innerHTML = `
            <div class="certificate-info">
                <i class="bi bi-file-earmark-lock"></i>
                <div class="file-details">
                    <div class="file-name">${fileName}</div>
                    <div class="file-size">${fileSize}</div>
                </div>
            </div>
        `;
    }
}

function setupColorPickers() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(colorInput => {
        const textInput = colorInput.parentNode.querySelector('input[type="text"]');
        
        if (textInput) {
            // Update text when color picker changes
            colorInput.addEventListener('input', function() {
                textInput.value = this.value.toUpperCase();
            });
            
            // Update color picker when text changes
            textInput.addEventListener('input', function() {
                if (isValidHexColor(this.value)) {
                    colorInput.value = this.value;
                }
            });
            
            // Validate hex color on blur
            textInput.addEventListener('blur', function() {
                if (!isValidHexColor(this.value)) {
                    this.value = colorInput.value.toUpperCase();
                }
            });
        }
    });
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

function isValidHexColor(hex) {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Currency formatting
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Form reset functionality
function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will reload the page and lose any unsaved changes.')) {
        location.reload();
    }
}

// Auto-save functionality (optional)
function setupAutoSave() {
    const form = document.getElementById('settingsForm');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input, select, textarea');
    let autoSaveTimeout;
    
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                saveFormData();
            }, 2000); // Auto-save after 2 seconds of inactivity
        });
    });
}

function saveFormData() {
    const form = document.getElementById('settingsForm');
    if (!form) return;
    
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // Save to localStorage as backup
    localStorage.setItem('bamboo_settings_backup', JSON.stringify(data));
    
    // Show auto-save indicator
    showNotification('Settings auto-saved', 'success');
}

function loadFormData() {
    const savedData = localStorage.getItem('bamboo_settings_backup');
    if (!savedData) return;
    
    try {
        const data = JSON.parse(savedData);
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        Object.keys(data).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input && input.type !== 'file') {
                input.value = data[key];
            }
        });
        
        showNotification('Previous settings restored from backup', 'info');
    } catch (error) {
        console.error('Failed to load form data:', error);
    }
}

// Initialize auto-save if needed
// setupAutoSave();

// Rich Text Editor Functions
function initializeRichTextEditor() {
    const editor = document.getElementById('contract_terms_editor');
    const hiddenTextarea = document.getElementById('contract_terms');

    if (editor && hiddenTextarea) {
        // Sync content from editor to hidden textarea
        editor.addEventListener('input', function() {
            hiddenTextarea.value = editor.innerHTML;
        });

        // Initialize hidden textarea with editor content
        hiddenTextarea.value = editor.innerHTML;

        // Handle form submission
        const form = document.getElementById('settingsForm');
        if (form) {
            form.addEventListener('submit', function() {
                hiddenTextarea.value = editor.innerHTML;
            });
        }

        // Handle toolbar button states
        editor.addEventListener('keyup', updateToolbarButtons);
        editor.addEventListener('mouseup', updateToolbarButtons);
    }
}

function formatText(command, value = null) {
    document.execCommand(command, false, value);

    // Update button states
    updateToolbarButtons();

    // Sync content
    const editor = document.getElementById('contract_terms_editor');
    const hiddenTextarea = document.getElementById('contract_terms');
    if (editor && hiddenTextarea) {
        hiddenTextarea.value = editor.innerHTML;
    }
}

function updateToolbarButtons() {
    const buttons = document.querySelectorAll('.editor-toolbar .btn');

    buttons.forEach(button => {
        const command = button.getAttribute('onclick');
        if (command) {
            const commandName = command.match(/formatText\('([^']+)'/);
            if (commandName) {
                const isActive = document.queryCommandState(commandName[1]);
                button.classList.toggle('active', isActive);
            }
        }
    });
}

// Initialize rich text editor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeRichTextEditor();
});
