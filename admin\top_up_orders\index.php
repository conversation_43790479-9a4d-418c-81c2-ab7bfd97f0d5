<?php
/**
 * Bamboo Web Application - Admin Top-Up Orders Page
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$page_title = 'Top-Up Orders';
$page_section = 'top_up_orders';

// Handle search and pagination
$search_username = isset($_GET['search_username']) ? trim($_GET['search_username']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;
$where = "WHERE (t.type IN ('admin_credit', 'deposit', 'adjustment') OR t.description LIKE '%admin%')";
$params = [];
if ($search_username !== '') {
    $where .= " AND u.username LIKE ?";
    $params[] = "%$search_username%";
}
$count_query = "SELECT COUNT(*) FROM transactions t JOIN users u ON t.user_id = u.id $where";
$db = getDB();
$stmt = $db->prepare($count_query);
$stmt->execute($params);
$total_orders = $stmt->fetchColumn();
$query = "SELECT t.*, u.username, u.email, u.phone, u.usdt_wallet_address
          FROM transactions t
          JOIN users u ON t.user_id = u.id
          $where
          ORDER BY t.created_at DESC
          LIMIT $per_page OFFSET $offset";
$top_up_orders = fetchAll($query, $params);
$total_pages = ceil($total_orders / $per_page);

include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><?php echo $page_title; ?></h1>
                </div>

                <div class="card">
                    <div class="card-header d-flex flex-wrap justify-content-between align-items-center">
                        <span>All Top-Up Orders</span>
                        <form class="d-flex" method="get" action="">
                            <input type="text" class="form-control form-control-sm me-2" name="search_username" placeholder="Search by Username" value="<?php echo htmlspecialchars($search_username); ?>" style="max-width:180px;">
                            <button class="btn btn-primary btn-sm" type="submit"><i class="bi bi-search"></i></button>
                        </form>
                    </div>
                    <div class="card-body">
                        <?php if (empty($top_up_orders)): ?>
                            <p>No top-up orders found.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover align-middle table-compact">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width:40px;">#</th>
                                            <th style="min-width:150px;max-width:180px;">Member Info</th>
                                            <th style="min-width:100px;max-width:130px;">Merchant Order No</th>
                                            <th style="width:90px;">Order Amount</th>
                                            <th style="width:110px;">Payment Channel</th>
                                            <th style="min-width:100px;max-width:140px;">Wallet Address</th>
                                            <th style="width:70px;">State</th>
                                            <th style="width:120px;">Creation Time</th>
                                            <th style="min-width:100px;max-width:160px;">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $serial_no = $offset + 1; ?>
                                        <?php foreach ($top_up_orders as $order): ?>
                                            <tr>
                                                <td><?php echo $serial_no++; ?></td>
                                                <td style="font-size:13px;line-height:1.3;word-break:break-all;">
                                                    <strong>User:</strong> <?php echo htmlspecialchars($order['username']); ?><br>
                                                    <span class="text-muted" style="font-size:12px;"><strong>Email:</strong> <?php echo htmlspecialchars($order['email']); ?><br>
                                                    <strong>Phone:</strong> <?php echo htmlspecialchars($order['phone']); ?></span>
                                                </td>
                                                <td style="font-size:13px;word-break:break-all;">
                                                    <?php echo htmlspecialchars($order['order_no'] ?? 'N/A'); ?>
                                                </td>
                                                <td style="font-size:13px;"><?php echo formatCurrency($order['amount']); ?></td>
                                                <td style="font-size:13px;"><?php echo htmlspecialchars($order['payment_method'] ?? $order['payment_channel'] ?? 'Admin Credit'); ?></td>
                                                <td style="font-size:13px;word-break:break-all;">
                                                    <?php echo htmlspecialchars($order['usdt_wallet_address'] ?? 'N/A'); ?>
                                                </td>
                                                <td><span class="badge bg-<?php echo getStatusBadgeClass($order['status']); ?>" style="font-size:12px;"><?php echo ucfirst($order['status']); ?></span></td>
                                                <td style="font-size:12px;"><?php echo formatDate($order['created_at']); ?></td>
                                                <td style="font-size:12px;word-break:break-all;">
                                                    <?php echo htmlspecialchars($order['description'] ?? 'N/A'); ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <!-- Pagination -->
                            <nav aria-label="Top-Up Orders Pagination" class="mt-3">
                                <ul class="pagination pagination-sm justify-content-end">
                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?php if ($i == $page) echo 'active'; ?>">
                                            <a class="page-link" href="?search_username=<?php echo urlencode($search_username); ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>
